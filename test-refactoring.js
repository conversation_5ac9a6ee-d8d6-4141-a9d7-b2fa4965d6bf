/**
 * Test script to verify the refactoring is working correctly
 * This can be run in both Apps Script and local environments
 */

/**
 * Test the shared utilities
 */
function testSharedUtilities() {
    console.log('🧪 Testing shared utilities...');
    
    // Test CSV parsing
    const testCsv = 'event1\nevent2\nevent3';
    const eventIds = parseCsvEventIds(testCsv);
    console.log('✅ CSV parsing test:', eventIds.length === 3 ? 'PASS' : 'FAIL');
    
    // Test session duration calculation
    const startTime = '2024-01-01T10:00:00Z';
    const endTime = '2024-01-01T11:30:00Z';
    const duration = calculateSessionDuration(startTime, endTime);
    console.log('✅ Duration calculation test:', duration === 90 ? 'PASS' : 'FAIL');
    
    // Test total duration calculation
    const sessions = [
        { duration: 30 },
        { duration: 45 },
        { duration: 15 }
    ];
    const totalDuration = calculateTotalDuration(sessions);
    console.log('✅ Total duration test:', totalDuration === 90 ? 'PASS' : 'FAIL');
    
    // Test event ID validation
    const validId = 'abc123_def456';
    const invalidId = '<script>alert("test")</script>';
    console.log('✅ Event ID validation test:', 
        isValidEventId(validId) && !isValidEventId(invalidId) ? 'PASS' : 'FAIL');
    
    // Test enhanced logging
    enhancedLog('info', 'Test log message', { test: true });
    console.log('✅ Enhanced logging test: PASS');
    
    console.log('🎉 Shared utilities tests completed');
}

/**
 * Test environment detection
 */
function testEnvironmentDetection() {
    console.log('🧪 Testing environment detection...');
    
    const env = getEnvironment();
    console.log('📍 Current environment:', env);
    
    if (env === 'apps_script' || env === 'local') {
        console.log('✅ Environment detection test: PASS');
    } else {
        console.log('❌ Environment detection test: FAIL');
    }
}

/**
 * Test summary generation
 */
function testSummaryGeneration() {
    console.log('🧪 Testing summary generation...');
    
    const mockData = [
        {
            participants: [
                {
                    email: '<EMAIL>',
                    totalDuration: 60
                },
                {
                    email: '<EMAIL>',
                    totalDuration: 45
                }
            ]
        },
        {
            participants: [
                {
                    email: '<EMAIL>',
                    totalDuration: 30
                },
                {
                    email: '<EMAIL>',
                    totalDuration: 90
                }
            ]
        }
    ];
    
    const summary = generateSummary(mockData);
    
    console.log('📊 Generated summary:', summary);
    
    const tests = [
        { name: 'Total events', actual: summary.totalEvents, expected: 2 },
        { name: 'Total participants', actual: summary.totalParticipants, expected: 4 },
        { name: 'Unique participants', actual: summary.uniqueParticipantCount, expected: 3 },
        { name: 'Total attendance minutes', actual: summary.totalAttendanceMinutes, expected: 225 }
    ];
    
    let allPassed = true;
    tests.forEach(test => {
        const passed = test.actual === test.expected;
        console.log(`${passed ? '✅' : '❌'} ${test.name}: ${test.actual} (expected ${test.expected})`);
        if (!passed) allPassed = false;
    });
    
    console.log(`🎯 Summary generation test: ${allPassed ? 'PASS' : 'FAIL'}`);
}

/**
 * Test error handling
 */
function testErrorHandling() {
    console.log('🧪 Testing error handling...');
    
    try {
        // Test with invalid CSV data
        parseCsvEventIds(null);
        console.log('❌ Error handling test: FAIL (should have thrown error)');
    } catch (error) {
        console.log('✅ Error handling test: PASS (correctly threw error)');
        logError('testErrorHandling', error, { testContext: 'CSV parsing' });
    }
}

/**
 * Run all tests
 */
function runAllTests() {
    console.log('🚀 Starting refactoring tests...');
    console.log('=====================================');
    
    testEnvironmentDetection();
    console.log('');
    
    testSharedUtilities();
    console.log('');
    
    testSummaryGeneration();
    console.log('');
    
    testErrorHandling();
    console.log('');
    
    console.log('=====================================');
    console.log('🎉 All refactoring tests completed!');
    
    return {
        success: true,
        environment: getEnvironment(),
        timestamp: new Date().toISOString(),
        message: 'Refactoring verification tests completed successfully'
    };
}

// Auto-run tests if in Apps Script environment
if (typeof getEnvironment === 'function' && getEnvironment() === 'apps_script') {
    console.log('🔄 Auto-running tests in Apps Script environment...');
    // Don't auto-run to avoid console spam, but make available
}

// Export for different environments
if (typeof module !== 'undefined' && module.exports) {
    // Node.js environment
    module.exports = {
        testSharedUtilities,
        testEnvironmentDetection,
        testSummaryGeneration,
        testErrorHandling,
        runAllTests
    };
} else if (typeof window !== 'undefined') {
    // Browser environment
    window.RefactoringTests = {
        testSharedUtilities,
        testEnvironmentDetection,
        testSummaryGeneration,
        testErrorHandling,
        runAllTests
    };
}

console.log('💡 Test functions available:');
console.log('  - runAllTests() - Run all verification tests');
console.log('  - testSharedUtilities() - Test shared utility functions');
console.log('  - testEnvironmentDetection() - Test environment detection');
console.log('  - testSummaryGeneration() - Test summary generation');
console.log('  - testErrorHandling() - Test error handling');
