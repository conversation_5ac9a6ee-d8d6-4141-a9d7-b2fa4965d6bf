# Google Calendar API Guide

This guide provides an overview and links to official documentation for using Google Calendar APIs, both within Google Apps Script and via its raw REST API.

## Section 1: Google Calendar Usage within Google Apps Script

Google Apps Script provides two main ways to interact with Google Calendar:

### 1. CalendarApp Service (Built-in)

The `CalendarApp` service is a built-in Apps Script service that allows you to create, find, and modify Google Calendar events, calendars, and guests. It's generally easier to use for common tasks directly within your scripts.

*   **Purpose:** Provides high-level methods for interacting with Google Calendar data.
*   **Official Documentation:** [CalendarApp Service Reference](https://developers.google.com/apps-script/reference/calendar/calendar-app)
*   **Key Classes:**
    *   `Calendar`: Represents a calendar.
    *   `CalendarEvent`: Represents an event on a calendar.
    *   `CalendarEventSeries`: Represents a recurring event.
    *   `EventGuest`: Represents a guest of an event.

### 2. Advanced Calendar API Service

The Advanced Calendar API service in Apps Script is a thin wrapper around the Google Calendar REST API. It gives you access to more features of the Calendar API that might not be available through the simpler `CalendarApp` service. You need to enable this advanced service in your Apps Script project before using it.

*   **Purpose:** Provides access to the full capabilities of the Google Calendar REST API from within Apps Script.
*   **Official Documentation:** [Advanced Calendar API Service Reference](https://developers.google.com/apps-script/advanced/calendar)
*   **Enabling the Service:** Refer to the [Enable Advanced Services guide](https://developers.google.com/apps-script/guides/services/advanced#enable_advanced_services).

## Section 2: Raw Google Calendar REST API

The Google Calendar REST API allows you to interact with Google Calendar programmatically using standard HTTP requests. This is useful for applications outside of the Google Apps Script environment or when you need direct control over API calls.

*   **Purpose:** Provides a full set of HTTP endpoints for creating, reading, updating, and deleting calendar data, managing access controls, and more.
*   **Official Documentation Hub:** [Google Calendar API v3 Reference](https://developers.google.com/calendar/api/v3/reference)
*   **Client Libraries:** Google provides client libraries for various languages. See the "Client Libraries" section on the [Calendar API overview page](https://developers.google.com/calendar/api/guides/overview).

### Key Concepts & Resources:

*   **Authentication & Authorization:**
    *   The Calendar API uses OAuth 2.0 for authorization. Different scopes are required for different levels of access.
    *   **Documentation:** [Authorize requests to the Google Calendar API](https://developers.google.com/calendar/api/guides/auth)

*   **Events:**
    *   An `Event` resource represents an event on a calendar.
    *   **Resource Representation:** [`Events`](https://developers.google.com/calendar/api/v3/reference/events)
    *   **Key Operations:**
        *   Create an event: [`events.insert`](https://developers.google.com/calendar/api/v3/reference/events/insert)
            *   **Key Parameters:** `calendarId`, `requestBody` (Event resource including `summary`, `start.dateTime`, `end.dateTime`, `attendees`, `conferenceData`, etc.), `sendNotifications`, `supportsAttachments`.
            *   **Returns:** The created `Event` resource.
        *   Get an event: [`events.get`](https://developers.google.com/calendar/api/v3/reference/events/get)
            *   **Key Parameters:** `calendarId`, `eventId`.
            *   **Returns:** The `Event` resource.
        *   List events: [`events.list`](https://developers.google.com/calendar/api/v3/reference/events/list)
            *   **Key Parameters:** `calendarId`, `timeMin`, `timeMax`, `q` (free text search), `orderBy`, `maxResults`, `pageToken`, `showDeleted`, `singleEvents` (for expanding recurring events).
            *   **Returns:** A list of `Event` resources and a `nextPageToken`.
        *   Update an event: [`events.update`](https://developers.google.com/calendar/api/v3/reference/events/update)
            *   **Key Parameters:** `calendarId`, `eventId`, `requestBody` (Event resource with fields to update), `sendNotifications`.
            *   **Returns:** The updated `Event` resource.
        *   Delete an event: [`events.delete`](https://developers.google.com/calendar/api/v3/reference/events/delete)
            *   **Key Parameters:** `calendarId`, `eventId`, `sendNotifications`.
            *   **Returns:** An empty response on success.
        *   Import events: [`events.import`](https://developers.google.com/calendar/api/v3/reference/events/import) (imports an event from an iCalendar (RFC5545) format)
            *   **Key Parameters:** `calendarId`, `requestBody` (Event resource with `iCalUID` and other fields).
            *   **Returns:** The imported `Event` resource.
    *   **Guides:** [Events Overview](https://developers.google.com/calendar/api/guides/events)

*   **Calendars:**
    *   A `Calendar` resource represents a single calendar (e.g., primary calendar, secondary calendars).
    *   **Resource Representation:** [`Calendars`](https://developers.google.com/calendar/api/v3/reference/calendars)
    *   **Key Operations:**
        *   Get a calendar: [`calendars.get`](https://developers.google.com/calendar/api/v3/reference/calendars/get)
            *   **Key Parameters:** `calendarId`.
            *   **Returns:** The `Calendar` resource.
        *   Create a calendar: [`calendars.insert`](https://developers.google.com/calendar/api/v3/reference/calendars/insert)
            *   **Key Parameters:** `requestBody` (Calendar resource, typically `summary`).
            *   **Returns:** The created `Calendar` resource.
        *   Update a calendar: [`calendars.patch`](https://developers.google.com/calendar/api/v3/reference/calendars/patch) (or `update`)
            *   **Key Parameters:** `calendarId`, `requestBody` (Calendar resource with fields to update).
            *   **Returns:** The updated `Calendar` resource.
        *   Delete a calendar: [`calendars.delete`](https://developers.google.com/calendar/api/v3/reference/calendars/delete)
            *   **Key Parameters:** `calendarId`.
            *   **Returns:** An empty response on success.
        *   Clear primary calendar: [`calendars.clear`](https://developers.google.com/calendar/api/v3/reference/calendars/clear) (deletes all events on the primary calendar)
            *   **Key Parameters:** `calendarId` (must be 'primary').
            *   **Returns:** An empty response on success.
    *   **Guides:** [Calendar Concepts](https://developers.google.com/calendar/api/concepts/calendars-and-events) (covers calendars and events)

*   **CalendarList:**
    *   Manages the list of calendars that appear on a user's Google Calendar web UI.
    *   **Resource Representation:** [`CalendarList`](https://developers.google.com/calendar/api/v3/reference/calendarList)
    *   **Key Operations:** `list`, `get`, `insert`, `update`, `patch`, `delete`.
    *   **Guides:** [Manage calendar list](https://developers.google.com/calendar/api/guides/calendarlist)

*   **Access Control Lists (ACLs):**
    *   Manages sharing of calendars.
    *   **Resource Representation:** [`Acl`](https://developers.google.com/calendar/api/v3/reference/acl)
    *   **Key Operations:** `list`, `get`, `insert`, `update`, `patch`, `delete`.
    *   **Guides:** [Manage ACL rules](https://developers.google.com/calendar/api/guides/acl)

*   **Other Resources:**
    *   **Colors:** [`Colors`](https://developers.google.com/calendar/api/v3/reference/colors) - Retrieve available calendar and event colors.
    *   **Freebusy:** [`Freebusy`](https://developers.google.com/calendar/api/v3/reference/freebusy) - Query free/busy information for a set of calendars.
    *   **Settings:** [`Settings`](https://developers.google.com/calendar/api/v3/reference/settings) - Manage user-specific calendar settings.

*   **Important Note:** The official documentation linked throughout this section is the authoritative source for all details regarding the raw Calendar REST API, including specific request/response formats, all available parameters, authentication scopes, and usage limits.
