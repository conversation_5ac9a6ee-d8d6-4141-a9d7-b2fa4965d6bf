/**
 * This file is auto-generated by go/maestro-externs.
 * @externs
 */

/** @constructor */
var DocumentApp = function() {};

/**
 * @param {string} name
 * @return {DocumentApp.Document}
 */
DocumentApp.create = function(name) {};

/**
 * @return {DocumentApp.Document}
 */
DocumentApp.getActiveDocument = function() {};

/**
 * @return {Ui}
 */
DocumentApp.getUi = function() {};

/**
 * @param {string} id
 * @return {DocumentApp.Document}
 */
DocumentApp.openById = function(id) {};

/**
 * @param {string} url
 * @return {DocumentApp.Document}
 */
DocumentApp.openByUrl = function(url) {};

/** @enum {string} */
DocumentApp.Attribute = {
  BACKGROUND_COLOR: '',
  BOLD: '',
  BORDER_COLOR: '',
  BORDER_WIDTH: '',
  CODE: '',
  FONT_FAMILY: '',
  FONT_SIZE: '',
  FOREGROUND_COLOR: '',
  GLYPH_TYPE: '',
  HEADING: '',
  HEIGHT: '',
  HORIZONTAL_ALIGNMENT: '',
  INDENT_END: '',
  INDENT_FIRST_LINE: '',
  INDENT_START: '',
  ITALIC: '',
  LEFT_TO_RIGHT: '',
  LINE_SPACING: '',
  LINK_URL: '',
  LIST_ID: '',
  MARGIN_BOTTOM: '',
  MARGIN_LEFT: '',
  MARGIN_RIGHT: '',
  MARGIN_TOP: '',
  MINIMUM_HEIGHT: '',
  NESTING_LEVEL: '',
  PADDING_BOTTOM: '',
  PADDING_LEFT: '',
  PADDING_RIGHT: '',
  PADDING_TOP: '',
  PAGE_HEIGHT: '',
  PAGE_WIDTH: '',
  SPACING_AFTER: '',
  SPACING_BEFORE: '',
  STRIKETHROUGH: '',
  UNDERLINE: '',
  VERTICAL_ALIGNMENT: '',
  WIDTH: ''
};

/** @constructor */
DocumentApp.Body = function() {};

/**
 * @return {DocumentApp.HorizontalRule}
 */
DocumentApp.Body.prototype.appendHorizontalRule = function() {};

/**
 * @param {BlobSource|DocumentApp.InlineImage} image
 * @return {DocumentApp.InlineImage}
 */
DocumentApp.Body.prototype.appendImage = function(image) {};

/**
 * @param {DocumentApp.ListItem|string} listItemOrText
 * @return {DocumentApp.ListItem}
 */
DocumentApp.Body.prototype.appendListItem = function(listItemOrText) {};

/**
 * @param {DocumentApp.PageBreak=} opt_pageBreak
 * @return {DocumentApp.PageBreak}
 */
DocumentApp.Body.prototype.appendPageBreak = function(opt_pageBreak) {};

/**
 * @param {DocumentApp.Paragraph|string} paragraphOrText
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.Body.prototype.appendParagraph = function(paragraphOrText) {};

/**
 * @param {Array<Array<string>>|DocumentApp.Table|undefined} opt_cellsOrTable
 * @return {DocumentApp.Table}
 */
DocumentApp.Body.prototype.appendTable = function(opt_cellsOrTable) {};

/**
 * @return {DocumentApp.Body}
 */
DocumentApp.Body.prototype.clear = function() {};

/**
 * @return {DocumentApp.Body}
 */
DocumentApp.Body.prototype.copy = function() {};

/**
 * @return {DocumentApp.Text}
 */
DocumentApp.Body.prototype.editAsText = function() {};

/**
 * @param {DocumentApp.ElementType} elementType
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.Body.prototype.findElement = function(elementType, opt_from) {};

/**
 * @param {string} searchPattern
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.Body.prototype.findText = function(searchPattern, opt_from) {};

/**
 * @return {Object}
 */
DocumentApp.Body.prototype.getAttributes = function() {};

/**
 * @param {number} childIndex
 * @return {DocumentApp.Element}
 */
DocumentApp.Body.prototype.getChild = function(childIndex) {};

/**
 * @param {DocumentApp.Element} child
 * @return {number}
 */
DocumentApp.Body.prototype.getChildIndex = function(child) {};

/**
 * @param {DocumentApp.ParagraphHeading} paragraphHeading
 * @return {Object}
 */
DocumentApp.Body.prototype.getHeadingAttributes = function(paragraphHeading) {};

/**
 * @return {Array<DocumentApp.InlineImage>}
 */
DocumentApp.Body.prototype.getImages = function() {};

/**
 * @return {Array<DocumentApp.ListItem>}
 */
DocumentApp.Body.prototype.getListItems = function() {};

/**
 * @return {number}
 */
DocumentApp.Body.prototype.getMarginBottom = function() {};

/**
 * @return {number}
 */
DocumentApp.Body.prototype.getMarginLeft = function() {};

/**
 * @return {number}
 */
DocumentApp.Body.prototype.getMarginRight = function() {};

/**
 * @return {number}
 */
DocumentApp.Body.prototype.getMarginTop = function() {};

/**
 * @return {number}
 */
DocumentApp.Body.prototype.getNumChildren = function() {};

/**
 * @return {number}
 */
DocumentApp.Body.prototype.getPageHeight = function() {};

/**
 * @return {number}
 */
DocumentApp.Body.prototype.getPageWidth = function() {};

/**
 * @return {Array<DocumentApp.Paragraph>}
 */
DocumentApp.Body.prototype.getParagraphs = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.Body.prototype.getParent = function() {};

/**
 * @return {Array<DocumentApp.Table>}
 */
DocumentApp.Body.prototype.getTables = function() {};

/**
 * @return {string}
 */
DocumentApp.Body.prototype.getText = function() {};

/**
 * @return {DocumentApp.TextAlignment}
 */
DocumentApp.Body.prototype.getTextAlignment = function() {};

/**
 * @return {DocumentApp.ElementType}
 */
DocumentApp.Body.prototype.getType = function() {};

/**
 * @param {number} childIndex
 * @return {DocumentApp.HorizontalRule}
 */
DocumentApp.Body.prototype.insertHorizontalRule = function(childIndex) {};

/**
 * @param {number} childIndex
 * @param {BlobSource|DocumentApp.InlineImage} image
 * @return {DocumentApp.InlineImage}
 */
DocumentApp.Body.prototype.insertImage = function(childIndex, image) {};

/**
 * @param {number} childIndex
 * @param {DocumentApp.ListItem|string} listItemOrText
 * @return {DocumentApp.ListItem}
 */
DocumentApp.Body.prototype.insertListItem = function(childIndex, listItemOrText) {};

/**
 * @param {number} childIndex
 * @param {DocumentApp.PageBreak=} opt_pageBreak
 * @return {DocumentApp.PageBreak}
 */
DocumentApp.Body.prototype.insertPageBreak = function(childIndex, opt_pageBreak) {};

/**
 * @param {number} childIndex
 * @param {DocumentApp.Paragraph|string} paragraphOrText
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.Body.prototype.insertParagraph = function(childIndex, paragraphOrText) {};

/**
 * @param {number} childIndex
 * @param {Array<Array<string>>|DocumentApp.Table|undefined} opt_cellsOrTable
 * @return {DocumentApp.Table}
 */
DocumentApp.Body.prototype.insertTable = function(childIndex, opt_cellsOrTable) {};

/**
 * @param {DocumentApp.Element} child
 * @return {DocumentApp.Body}
 */
DocumentApp.Body.prototype.removeChild = function(child) {};

/**
 * @param {string} searchPattern
 * @param {string} replacement
 * @return {DocumentApp.Element}
 */
DocumentApp.Body.prototype.replaceText = function(searchPattern, replacement) {};

/**
 * @param {Object} attributes
 * @return {DocumentApp.Body}
 */
DocumentApp.Body.prototype.setAttributes = function(attributes) {};

/**
 * @param {DocumentApp.ParagraphHeading} paragraphHeading
 * @param {Object} attributes
 * @return {DocumentApp.Body}
 */
DocumentApp.Body.prototype.setHeadingAttributes = function(paragraphHeading, attributes) {};

/**
 * @param {number} marginBottom
 * @return {DocumentApp.Body}
 */
DocumentApp.Body.prototype.setMarginBottom = function(marginBottom) {};

/**
 * @param {number} marginLeft
 * @return {DocumentApp.Body}
 */
DocumentApp.Body.prototype.setMarginLeft = function(marginLeft) {};

/**
 * @param {number} marginRight
 * @return {DocumentApp.Body}
 */
DocumentApp.Body.prototype.setMarginRight = function(marginRight) {};

/**
 * @param {number} marginTop
 * @return {DocumentApp.Body}
 */
DocumentApp.Body.prototype.setMarginTop = function(marginTop) {};

/**
 * @param {number} pageHeight
 * @return {DocumentApp.Body}
 */
DocumentApp.Body.prototype.setPageHeight = function(pageHeight) {};

/**
 * @param {number} pageWidth
 * @return {DocumentApp.Body}
 */
DocumentApp.Body.prototype.setPageWidth = function(pageWidth) {};

/**
 * @param {string} text
 * @return {DocumentApp.Body}
 */
DocumentApp.Body.prototype.setText = function(text) {};

/**
 * @param {DocumentApp.TextAlignment} textAlignment
 * @return {DocumentApp.Body}
 */
DocumentApp.Body.prototype.setTextAlignment = function(textAlignment) {};

/** @constructor */
DocumentApp.Bookmark = function() {};

/**
 * @return {string}
 */
DocumentApp.Bookmark.prototype.getId = function() {};

/**
 * @return {DocumentApp.Position}
 */
DocumentApp.Bookmark.prototype.getPosition = function() {};

/**
 */
DocumentApp.Bookmark.prototype.remove = function() {};

/** @constructor */
DocumentApp.ContainerElement = function() {};

/**
 * @return {DocumentApp.Body}
 */
DocumentApp.ContainerElement.prototype.asBody = function() {};

/**
 * @return {DocumentApp.Equation}
 */
DocumentApp.ContainerElement.prototype.asEquation = function() {};

/**
 * @return {DocumentApp.FooterSection}
 */
DocumentApp.ContainerElement.prototype.asFooterSection = function() {};

/**
 * @return {DocumentApp.FootnoteSection}
 */
DocumentApp.ContainerElement.prototype.asFootnoteSection = function() {};

/**
 * @return {DocumentApp.HeaderSection}
 */
DocumentApp.ContainerElement.prototype.asHeaderSection = function() {};

/**
 * @return {DocumentApp.ListItem}
 */
DocumentApp.ContainerElement.prototype.asListItem = function() {};

/**
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.ContainerElement.prototype.asParagraph = function() {};

/**
 * @return {DocumentApp.Table}
 */
DocumentApp.ContainerElement.prototype.asTable = function() {};

/**
 * @return {DocumentApp.TableCell}
 */
DocumentApp.ContainerElement.prototype.asTableCell = function() {};

/**
 * @return {DocumentApp.TableOfContents}
 */
DocumentApp.ContainerElement.prototype.asTableOfContents = function() {};

/**
 * @return {DocumentApp.TableRow}
 */
DocumentApp.ContainerElement.prototype.asTableRow = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.ContainerElement.prototype.clear = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.ContainerElement.prototype.copy = function() {};

/**
 * @return {DocumentApp.Text}
 */
DocumentApp.ContainerElement.prototype.editAsText = function() {};

/**
 * @param {DocumentApp.ElementType} elementType
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.ContainerElement.prototype.findElement = function(elementType, opt_from) {};

/**
 * @param {string} searchPattern
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.ContainerElement.prototype.findText = function(searchPattern, opt_from) {};

/**
 * @return {Object}
 */
DocumentApp.ContainerElement.prototype.getAttributes = function() {};

/**
 * @param {number} childIndex
 * @return {DocumentApp.Element}
 */
DocumentApp.ContainerElement.prototype.getChild = function(childIndex) {};

/**
 * @param {DocumentApp.Element} child
 * @return {number}
 */
DocumentApp.ContainerElement.prototype.getChildIndex = function(child) {};

/**
 * @return {string}
 */
DocumentApp.ContainerElement.prototype.getLinkUrl = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.ContainerElement.prototype.getNextSibling = function() {};

/**
 * @return {number}
 */
DocumentApp.ContainerElement.prototype.getNumChildren = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.ContainerElement.prototype.getParent = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.ContainerElement.prototype.getPreviousSibling = function() {};

/**
 * @return {string}
 */
DocumentApp.ContainerElement.prototype.getText = function() {};

/**
 * @return {DocumentApp.TextAlignment}
 */
DocumentApp.ContainerElement.prototype.getTextAlignment = function() {};

/**
 * @return {DocumentApp.ElementType}
 */
DocumentApp.ContainerElement.prototype.getType = function() {};

/**
 * @return {boolean}
 */
DocumentApp.ContainerElement.prototype.isAtDocumentEnd = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.ContainerElement.prototype.merge = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.ContainerElement.prototype.removeFromParent = function() {};

/**
 * @param {string} searchPattern
 * @param {string} replacement
 * @return {DocumentApp.Element}
 */
DocumentApp.ContainerElement.prototype.replaceText = function(searchPattern, replacement) {};

/**
 * @param {Object} attributes
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.ContainerElement.prototype.setAttributes = function(attributes) {};

/**
 * @param {string} url
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.ContainerElement.prototype.setLinkUrl = function(url) {};

/**
 * @param {DocumentApp.TextAlignment} textAlignment
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.ContainerElement.prototype.setTextAlignment = function(textAlignment) {};

/** @constructor */
DocumentApp.Date = function() {};

/**
 * @return {DocumentApp.Date}
 */
DocumentApp.Date.prototype.copy = function() {};

/**
 * @return {Object}
 */
DocumentApp.Date.prototype.getAttributes = function() {};

/**
 * @return {string}
 */
DocumentApp.Date.prototype.getDisplayText = function() {};

/**
 * @return {string}
 */
DocumentApp.Date.prototype.getLocale = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.Date.prototype.getNextSibling = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.Date.prototype.getParent = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.Date.prototype.getPreviousSibling = function() {};

/**
 * @return {Date}
 */
DocumentApp.Date.prototype.getTimestamp = function() {};

/**
 * @return {DocumentApp.ElementType}
 */
DocumentApp.Date.prototype.getType = function() {};

/**
 * @return {boolean}
 */
DocumentApp.Date.prototype.isAtDocumentEnd = function() {};

/**
 * @return {DocumentApp.Date}
 */
DocumentApp.Date.prototype.merge = function() {};

/**
 * @return {DocumentApp.Date}
 */
DocumentApp.Date.prototype.removeFromParent = function() {};

/**
 * @param {Object} attributes
 * @return {DocumentApp.Date}
 */
DocumentApp.Date.prototype.setAttributes = function(attributes) {};

/** @constructor */
DocumentApp.Document = function() {};

/**
 * @param {DocumentApp.Position} position
 * @return {DocumentApp.Bookmark}
 */
DocumentApp.Document.prototype.addBookmark = function(position) {};

/**
 * @param {User|string} emailAddressOrUser
 * @return {DocumentApp.Document}
 */
DocumentApp.Document.prototype.addEditor = function(emailAddressOrUser) {};

/**
 * @param {Array<string>} emailAddresses
 * @return {DocumentApp.Document}
 */
DocumentApp.Document.prototype.addEditors = function(emailAddresses) {};

/**
 * @return {DocumentApp.FooterSection}
 */
DocumentApp.Document.prototype.addFooter = function() {};

/**
 * @return {DocumentApp.HeaderSection}
 */
DocumentApp.Document.prototype.addHeader = function() {};

/**
 * @param {string} name
 * @param {DocumentApp.Range} range
 * @return {DocumentApp.NamedRange}
 */
DocumentApp.Document.prototype.addNamedRange = function(name, range) {};

/**
 * @param {User|string} emailAddressOrUser
 * @return {DocumentApp.Document}
 */
DocumentApp.Document.prototype.addViewer = function(emailAddressOrUser) {};

/**
 * @param {Array<string>} emailAddresses
 * @return {DocumentApp.Document}
 */
DocumentApp.Document.prototype.addViewers = function(emailAddresses) {};

/**
 * @param {string} contentType
 * @return {Blob}
 */
DocumentApp.Document.prototype.getAs = function(contentType) {};

/**
 * @return {Blob}
 */
DocumentApp.Document.prototype.getBlob = function() {};

/**
 * @return {DocumentApp.Body}
 */
DocumentApp.Document.prototype.getBody = function() {};

/**
 * @param {string} id
 * @return {DocumentApp.Bookmark}
 */
DocumentApp.Document.prototype.getBookmark = function(id) {};

/**
 * @return {Array<DocumentApp.Bookmark>}
 */
DocumentApp.Document.prototype.getBookmarks = function() {};

/**
 * @return {DocumentApp.Position}
 */
DocumentApp.Document.prototype.getCursor = function() {};

/**
 * @return {Array<User>}
 */
DocumentApp.Document.prototype.getEditors = function() {};

/**
 * @return {DocumentApp.FooterSection}
 */
DocumentApp.Document.prototype.getFooter = function() {};

/**
 * @return {Array<DocumentApp.Footnote>}
 */
DocumentApp.Document.prototype.getFootnotes = function() {};

/**
 * @return {DocumentApp.HeaderSection}
 */
DocumentApp.Document.prototype.getHeader = function() {};

/**
 * @return {string}
 */
DocumentApp.Document.prototype.getId = function() {};

/**
 * @return {string}
 */
DocumentApp.Document.prototype.getLanguage = function() {};

/**
 * @return {string}
 */
DocumentApp.Document.prototype.getName = function() {};

/**
 * @param {string} id
 * @return {DocumentApp.NamedRange}
 */
DocumentApp.Document.prototype.getNamedRangeById = function(id) {};

/**
 * @param {string=} opt_name
 * @return {Array<DocumentApp.NamedRange>}
 */
DocumentApp.Document.prototype.getNamedRanges = function(opt_name) {};

/**
 * @return {DocumentApp.Range}
 */
DocumentApp.Document.prototype.getSelection = function() {};

/**
 * @return {Array<string>}
 */
DocumentApp.Document.prototype.getSupportedLanguageCodes = function() {};

/**
 * @return {string}
 */
DocumentApp.Document.prototype.getUrl = function() {};

/**
 * @return {Array<User>}
 */
DocumentApp.Document.prototype.getViewers = function() {};

/**
 * @param {DocumentApp.Element} element
 * @param {number} offset
 * @return {DocumentApp.Position}
 */
DocumentApp.Document.prototype.newPosition = function(element, offset) {};

/**
 * @return {DocumentApp.RangeBuilder}
 */
DocumentApp.Document.prototype.newRange = function() {};

/**
 * @param {User|string} emailAddressOrUser
 * @return {DocumentApp.Document}
 */
DocumentApp.Document.prototype.removeEditor = function(emailAddressOrUser) {};

/**
 * @param {User|string} emailAddressOrUser
 * @return {DocumentApp.Document}
 */
DocumentApp.Document.prototype.removeViewer = function(emailAddressOrUser) {};

/**
 */
DocumentApp.Document.prototype.saveAndClose = function() {};

/**
 * @param {DocumentApp.Position} position
 * @return {DocumentApp.Document}
 */
DocumentApp.Document.prototype.setCursor = function(position) {};

/**
 * @param {string} languageCode
 * @return {DocumentApp.Document}
 */
DocumentApp.Document.prototype.setLanguage = function(languageCode) {};

/**
 * @param {string} name
 * @return {DocumentApp.Document}
 */
DocumentApp.Document.prototype.setName = function(name) {};

/**
 * @param {DocumentApp.Range} range
 * @return {DocumentApp.Document}
 */
DocumentApp.Document.prototype.setSelection = function(range) {};

/** @constructor */
DocumentApp.Element = function() {};

/**
 * @return {DocumentApp.Body}
 */
DocumentApp.Element.prototype.asBody = function() {};

/**
 * @return {DocumentApp.Date}
 */
DocumentApp.Element.prototype.asDate = function() {};

/**
 * @return {DocumentApp.Equation}
 */
DocumentApp.Element.prototype.asEquation = function() {};

/**
 * @return {DocumentApp.EquationFunction}
 */
DocumentApp.Element.prototype.asEquationFunction = function() {};

/**
 * @return {DocumentApp.EquationFunctionArgumentSeparator}
 */
DocumentApp.Element.prototype.asEquationFunctionArgumentSeparator = function() {};

/**
 * @return {DocumentApp.EquationSymbol}
 */
DocumentApp.Element.prototype.asEquationSymbol = function() {};

/**
 * @return {DocumentApp.FooterSection}
 */
DocumentApp.Element.prototype.asFooterSection = function() {};

/**
 * @return {DocumentApp.Footnote}
 */
DocumentApp.Element.prototype.asFootnote = function() {};

/**
 * @return {DocumentApp.FootnoteSection}
 */
DocumentApp.Element.prototype.asFootnoteSection = function() {};

/**
 * @return {DocumentApp.HeaderSection}
 */
DocumentApp.Element.prototype.asHeaderSection = function() {};

/**
 * @return {DocumentApp.HorizontalRule}
 */
DocumentApp.Element.prototype.asHorizontalRule = function() {};

/**
 * @return {DocumentApp.InlineDrawing}
 */
DocumentApp.Element.prototype.asInlineDrawing = function() {};

/**
 * @return {DocumentApp.InlineImage}
 */
DocumentApp.Element.prototype.asInlineImage = function() {};

/**
 * @return {DocumentApp.ListItem}
 */
DocumentApp.Element.prototype.asListItem = function() {};

/**
 * @return {DocumentApp.PageBreak}
 */
DocumentApp.Element.prototype.asPageBreak = function() {};

/**
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.Element.prototype.asParagraph = function() {};

/**
 * @return {DocumentApp.Person}
 */
DocumentApp.Element.prototype.asPerson = function() {};

/**
 * @return {DocumentApp.RichLink}
 */
DocumentApp.Element.prototype.asRichLink = function() {};

/**
 * @return {DocumentApp.Table}
 */
DocumentApp.Element.prototype.asTable = function() {};

/**
 * @return {DocumentApp.TableCell}
 */
DocumentApp.Element.prototype.asTableCell = function() {};

/**
 * @return {DocumentApp.TableOfContents}
 */
DocumentApp.Element.prototype.asTableOfContents = function() {};

/**
 * @return {DocumentApp.TableRow}
 */
DocumentApp.Element.prototype.asTableRow = function() {};

/**
 * @return {DocumentApp.Text}
 */
DocumentApp.Element.prototype.asText = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.Element.prototype.copy = function() {};

/**
 * @return {Object}
 */
DocumentApp.Element.prototype.getAttributes = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.Element.prototype.getNextSibling = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.Element.prototype.getParent = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.Element.prototype.getPreviousSibling = function() {};

/**
 * @return {DocumentApp.ElementType}
 */
DocumentApp.Element.prototype.getType = function() {};

/**
 * @return {boolean}
 */
DocumentApp.Element.prototype.isAtDocumentEnd = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.Element.prototype.merge = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.Element.prototype.removeFromParent = function() {};

/**
 * @param {Object} attributes
 * @return {DocumentApp.Element}
 */
DocumentApp.Element.prototype.setAttributes = function(attributes) {};

/** @enum {string} */
DocumentApp.ElementType = {
  BODY_SECTION: '',
  COMMENT_SECTION: '',
  DATE: '',
  DOCUMENT: '',
  EQUATION: '',
  EQUATION_FUNCTION: '',
  EQUATION_FUNCTION_ARGUMENT_SEPARATOR: '',
  EQUATION_SYMBOL: '',
  FOOTER_SECTION: '',
  FOOTNOTE: '',
  FOOTNOTE_SECTION: '',
  HEADER_SECTION: '',
  HORIZONTAL_RULE: '',
  INLINE_DRAWING: '',
  INLINE_IMAGE: '',
  LIST_ITEM: '',
  PAGE_BREAK: '',
  PARAGRAPH: '',
  PERSON: '',
  RICH_LINK: '',
  TABLE: '',
  TABLE_CELL: '',
  TABLE_OF_CONTENTS: '',
  TABLE_ROW: '',
  TEXT: '',
  UNSUPPORTED: ''
};

/** @constructor */
DocumentApp.Equation = function() {};

/**
 * @return {DocumentApp.Equation}
 */
DocumentApp.Equation.prototype.clear = function() {};

/**
 * @return {DocumentApp.Equation}
 */
DocumentApp.Equation.prototype.copy = function() {};

/**
 * @return {DocumentApp.Text}
 */
DocumentApp.Equation.prototype.editAsText = function() {};

/**
 * @param {DocumentApp.ElementType} elementType
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.Equation.prototype.findElement = function(elementType, opt_from) {};

/**
 * @param {string} searchPattern
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.Equation.prototype.findText = function(searchPattern, opt_from) {};

/**
 * @return {Object}
 */
DocumentApp.Equation.prototype.getAttributes = function() {};

/**
 * @param {number} childIndex
 * @return {DocumentApp.Element}
 */
DocumentApp.Equation.prototype.getChild = function(childIndex) {};

/**
 * @param {DocumentApp.Element} child
 * @return {number}
 */
DocumentApp.Equation.prototype.getChildIndex = function(child) {};

/**
 * @return {string}
 */
DocumentApp.Equation.prototype.getLinkUrl = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.Equation.prototype.getNextSibling = function() {};

/**
 * @return {number}
 */
DocumentApp.Equation.prototype.getNumChildren = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.Equation.prototype.getParent = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.Equation.prototype.getPreviousSibling = function() {};

/**
 * @return {string}
 */
DocumentApp.Equation.prototype.getText = function() {};

/**
 * @return {DocumentApp.TextAlignment}
 */
DocumentApp.Equation.prototype.getTextAlignment = function() {};

/**
 * @return {DocumentApp.ElementType}
 */
DocumentApp.Equation.prototype.getType = function() {};

/**
 * @return {boolean}
 */
DocumentApp.Equation.prototype.isAtDocumentEnd = function() {};

/**
 * @return {DocumentApp.Equation}
 */
DocumentApp.Equation.prototype.merge = function() {};

/**
 * @return {DocumentApp.Equation}
 */
DocumentApp.Equation.prototype.removeFromParent = function() {};

/**
 * @param {string} searchPattern
 * @param {string} replacement
 * @return {DocumentApp.Element}
 */
DocumentApp.Equation.prototype.replaceText = function(searchPattern, replacement) {};

/**
 * @param {Object} attributes
 * @return {DocumentApp.Equation}
 */
DocumentApp.Equation.prototype.setAttributes = function(attributes) {};

/**
 * @param {string} url
 * @return {DocumentApp.Equation}
 */
DocumentApp.Equation.prototype.setLinkUrl = function(url) {};

/**
 * @param {DocumentApp.TextAlignment} textAlignment
 * @return {DocumentApp.Equation}
 */
DocumentApp.Equation.prototype.setTextAlignment = function(textAlignment) {};

/** @constructor */
DocumentApp.EquationFunction = function() {};

/**
 * @return {DocumentApp.EquationFunction}
 */
DocumentApp.EquationFunction.prototype.clear = function() {};

/**
 * @return {DocumentApp.EquationFunction}
 */
DocumentApp.EquationFunction.prototype.copy = function() {};

/**
 * @return {DocumentApp.Text}
 */
DocumentApp.EquationFunction.prototype.editAsText = function() {};

/**
 * @param {DocumentApp.ElementType} elementType
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.EquationFunction.prototype.findElement = function(elementType, opt_from) {};

/**
 * @param {string} searchPattern
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.EquationFunction.prototype.findText = function(searchPattern, opt_from) {};

/**
 * @return {Object}
 */
DocumentApp.EquationFunction.prototype.getAttributes = function() {};

/**
 * @param {number} childIndex
 * @return {DocumentApp.Element}
 */
DocumentApp.EquationFunction.prototype.getChild = function(childIndex) {};

/**
 * @param {DocumentApp.Element} child
 * @return {number}
 */
DocumentApp.EquationFunction.prototype.getChildIndex = function(child) {};

/**
 * @return {string}
 */
DocumentApp.EquationFunction.prototype.getCode = function() {};

/**
 * @return {string}
 */
DocumentApp.EquationFunction.prototype.getLinkUrl = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.EquationFunction.prototype.getNextSibling = function() {};

/**
 * @return {number}
 */
DocumentApp.EquationFunction.prototype.getNumChildren = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.EquationFunction.prototype.getParent = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.EquationFunction.prototype.getPreviousSibling = function() {};

/**
 * @return {string}
 */
DocumentApp.EquationFunction.prototype.getText = function() {};

/**
 * @return {DocumentApp.TextAlignment}
 */
DocumentApp.EquationFunction.prototype.getTextAlignment = function() {};

/**
 * @return {DocumentApp.ElementType}
 */
DocumentApp.EquationFunction.prototype.getType = function() {};

/**
 * @return {boolean}
 */
DocumentApp.EquationFunction.prototype.isAtDocumentEnd = function() {};

/**
 * @return {DocumentApp.EquationFunction}
 */
DocumentApp.EquationFunction.prototype.merge = function() {};

/**
 * @return {DocumentApp.EquationFunction}
 */
DocumentApp.EquationFunction.prototype.removeFromParent = function() {};

/**
 * @param {string} searchPattern
 * @param {string} replacement
 * @return {DocumentApp.Element}
 */
DocumentApp.EquationFunction.prototype.replaceText = function(searchPattern, replacement) {};

/**
 * @param {Object} attributes
 * @return {DocumentApp.EquationFunction}
 */
DocumentApp.EquationFunction.prototype.setAttributes = function(attributes) {};

/**
 * @param {string} url
 * @return {DocumentApp.EquationFunction}
 */
DocumentApp.EquationFunction.prototype.setLinkUrl = function(url) {};

/**
 * @param {DocumentApp.TextAlignment} textAlignment
 * @return {DocumentApp.EquationFunction}
 */
DocumentApp.EquationFunction.prototype.setTextAlignment = function(textAlignment) {};

/** @constructor */
DocumentApp.EquationFunctionArgumentSeparator = function() {};

/**
 * @return {DocumentApp.EquationFunctionArgumentSeparator}
 */
DocumentApp.EquationFunctionArgumentSeparator.prototype.copy = function() {};

/**
 * @return {Object}
 */
DocumentApp.EquationFunctionArgumentSeparator.prototype.getAttributes = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.EquationFunctionArgumentSeparator.prototype.getNextSibling = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.EquationFunctionArgumentSeparator.prototype.getParent = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.EquationFunctionArgumentSeparator.prototype.getPreviousSibling = function() {};

/**
 * @return {DocumentApp.ElementType}
 */
DocumentApp.EquationFunctionArgumentSeparator.prototype.getType = function() {};

/**
 * @return {boolean}
 */
DocumentApp.EquationFunctionArgumentSeparator.prototype.isAtDocumentEnd = function() {};

/**
 * @return {DocumentApp.EquationFunctionArgumentSeparator}
 */
DocumentApp.EquationFunctionArgumentSeparator.prototype.merge = function() {};

/**
 * @return {DocumentApp.EquationFunctionArgumentSeparator}
 */
DocumentApp.EquationFunctionArgumentSeparator.prototype.removeFromParent = function() {};

/**
 * @param {Object} attributes
 * @return {DocumentApp.EquationFunctionArgumentSeparator}
 */
DocumentApp.EquationFunctionArgumentSeparator.prototype.setAttributes = function(attributes) {};

/** @constructor */
DocumentApp.EquationSymbol = function() {};

/**
 * @return {DocumentApp.EquationSymbol}
 */
DocumentApp.EquationSymbol.prototype.copy = function() {};

/**
 * @return {Object}
 */
DocumentApp.EquationSymbol.prototype.getAttributes = function() {};

/**
 * @return {string}
 */
DocumentApp.EquationSymbol.prototype.getCode = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.EquationSymbol.prototype.getNextSibling = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.EquationSymbol.prototype.getParent = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.EquationSymbol.prototype.getPreviousSibling = function() {};

/**
 * @return {DocumentApp.ElementType}
 */
DocumentApp.EquationSymbol.prototype.getType = function() {};

/**
 * @return {boolean}
 */
DocumentApp.EquationSymbol.prototype.isAtDocumentEnd = function() {};

/**
 * @return {DocumentApp.EquationSymbol}
 */
DocumentApp.EquationSymbol.prototype.merge = function() {};

/**
 * @return {DocumentApp.EquationSymbol}
 */
DocumentApp.EquationSymbol.prototype.removeFromParent = function() {};

/**
 * @param {Object} attributes
 * @return {DocumentApp.EquationSymbol}
 */
DocumentApp.EquationSymbol.prototype.setAttributes = function(attributes) {};

/** @enum {string} */
DocumentApp.FontFamily = {
  AMARANTH: '',
  ARIAL: '',
  ARIAL_BLACK: '',
  ARIAL_NARROW: '',
  ARVO: '',
  CALIBRI: '',
  CAMBRIA: '',
  COMIC_SANS_MS: '',
  CONSOLAS: '',
  CORSIVA: '',
  COURIER_NEW: '',
  DANCING_SCRIPT: '',
  DROID_SANS: '',
  DROID_SERIF: '',
  GARAMOND: '',
  GEORGIA: '',
  GLORIA_HALLELUJAH: '',
  GREAT_VIBES: '',
  LOBSTER: '',
  MERRIWEATHER: '',
  PACIFICO: '',
  PHILOSOPHER: '',
  POIRET_ONE: '',
  QUATTROCENTO: '',
  ROBOTO: '',
  SHADOWS_INTO_LIGHT: '',
  SYNCOPATE: '',
  TAHOMA: '',
  TIMES_NEW_ROMAN: '',
  TREBUCHET_MS: '',
  UBUNTU: '',
  VERDANA: ''
};

/** @constructor */
DocumentApp.FooterSection = function() {};

/**
 * @return {DocumentApp.HorizontalRule}
 */
DocumentApp.FooterSection.prototype.appendHorizontalRule = function() {};

/**
 * @param {BlobSource|DocumentApp.InlineImage} image
 * @return {DocumentApp.InlineImage}
 */
DocumentApp.FooterSection.prototype.appendImage = function(image) {};

/**
 * @param {DocumentApp.ListItem|string} listItemOrText
 * @return {DocumentApp.ListItem}
 */
DocumentApp.FooterSection.prototype.appendListItem = function(listItemOrText) {};

/**
 * @param {DocumentApp.Paragraph|string} paragraphOrText
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.FooterSection.prototype.appendParagraph = function(paragraphOrText) {};

/**
 * @param {Array<Array<string>>|DocumentApp.Table|undefined} opt_cellsOrTable
 * @return {DocumentApp.Table}
 */
DocumentApp.FooterSection.prototype.appendTable = function(opt_cellsOrTable) {};

/**
 * @return {DocumentApp.FooterSection}
 */
DocumentApp.FooterSection.prototype.clear = function() {};

/**
 * @return {DocumentApp.FooterSection}
 */
DocumentApp.FooterSection.prototype.copy = function() {};

/**
 * @return {DocumentApp.Text}
 */
DocumentApp.FooterSection.prototype.editAsText = function() {};

/**
 * @param {DocumentApp.ElementType} elementType
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.FooterSection.prototype.findElement = function(elementType, opt_from) {};

/**
 * @param {string} searchPattern
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.FooterSection.prototype.findText = function(searchPattern, opt_from) {};

/**
 * @return {Object}
 */
DocumentApp.FooterSection.prototype.getAttributes = function() {};

/**
 * @param {number} childIndex
 * @return {DocumentApp.Element}
 */
DocumentApp.FooterSection.prototype.getChild = function(childIndex) {};

/**
 * @param {DocumentApp.Element} child
 * @return {number}
 */
DocumentApp.FooterSection.prototype.getChildIndex = function(child) {};

/**
 * @return {Array<DocumentApp.InlineImage>}
 */
DocumentApp.FooterSection.prototype.getImages = function() {};

/**
 * @return {Array<DocumentApp.ListItem>}
 */
DocumentApp.FooterSection.prototype.getListItems = function() {};

/**
 * @return {number}
 */
DocumentApp.FooterSection.prototype.getNumChildren = function() {};

/**
 * @return {Array<DocumentApp.Paragraph>}
 */
DocumentApp.FooterSection.prototype.getParagraphs = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.FooterSection.prototype.getParent = function() {};

/**
 * @return {Array<DocumentApp.Table>}
 */
DocumentApp.FooterSection.prototype.getTables = function() {};

/**
 * @return {string}
 */
DocumentApp.FooterSection.prototype.getText = function() {};

/**
 * @return {DocumentApp.TextAlignment}
 */
DocumentApp.FooterSection.prototype.getTextAlignment = function() {};

/**
 * @return {DocumentApp.ElementType}
 */
DocumentApp.FooterSection.prototype.getType = function() {};

/**
 * @param {number} childIndex
 * @return {DocumentApp.HorizontalRule}
 */
DocumentApp.FooterSection.prototype.insertHorizontalRule = function(childIndex) {};

/**
 * @param {number} childIndex
 * @param {BlobSource|DocumentApp.InlineImage} image
 * @return {DocumentApp.InlineImage}
 */
DocumentApp.FooterSection.prototype.insertImage = function(childIndex, image) {};

/**
 * @param {number} childIndex
 * @param {DocumentApp.ListItem|string} listItemOrText
 * @return {DocumentApp.ListItem}
 */
DocumentApp.FooterSection.prototype.insertListItem = function(childIndex, listItemOrText) {};

/**
 * @param {number} childIndex
 * @param {DocumentApp.Paragraph|string} paragraphOrText
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.FooterSection.prototype.insertParagraph = function(childIndex, paragraphOrText) {};

/**
 * @param {number} childIndex
 * @param {Array<Array<string>>|DocumentApp.Table|undefined} opt_cellsOrTable
 * @return {DocumentApp.Table}
 */
DocumentApp.FooterSection.prototype.insertTable = function(childIndex, opt_cellsOrTable) {};

/**
 * @param {DocumentApp.Element} child
 * @return {DocumentApp.FooterSection}
 */
DocumentApp.FooterSection.prototype.removeChild = function(child) {};

/**
 * @return {DocumentApp.FooterSection}
 */
DocumentApp.FooterSection.prototype.removeFromParent = function() {};

/**
 * @param {string} searchPattern
 * @param {string} replacement
 * @return {DocumentApp.Element}
 */
DocumentApp.FooterSection.prototype.replaceText = function(searchPattern, replacement) {};

/**
 * @param {Object} attributes
 * @return {DocumentApp.FooterSection}
 */
DocumentApp.FooterSection.prototype.setAttributes = function(attributes) {};

/**
 * @param {string} text
 * @return {DocumentApp.FooterSection}
 */
DocumentApp.FooterSection.prototype.setText = function(text) {};

/**
 * @param {DocumentApp.TextAlignment} textAlignment
 * @return {DocumentApp.FooterSection}
 */
DocumentApp.FooterSection.prototype.setTextAlignment = function(textAlignment) {};

/** @constructor */
DocumentApp.Footnote = function() {};

/**
 * @return {DocumentApp.Footnote}
 */
DocumentApp.Footnote.prototype.copy = function() {};

/**
 * @return {Object}
 */
DocumentApp.Footnote.prototype.getAttributes = function() {};

/**
 * @return {DocumentApp.FootnoteSection}
 */
DocumentApp.Footnote.prototype.getFootnoteContents = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.Footnote.prototype.getNextSibling = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.Footnote.prototype.getParent = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.Footnote.prototype.getPreviousSibling = function() {};

/**
 * @return {DocumentApp.ElementType}
 */
DocumentApp.Footnote.prototype.getType = function() {};

/**
 * @return {boolean}
 */
DocumentApp.Footnote.prototype.isAtDocumentEnd = function() {};

/**
 * @return {DocumentApp.Footnote}
 */
DocumentApp.Footnote.prototype.removeFromParent = function() {};

/**
 * @param {Object} attributes
 * @return {DocumentApp.Footnote}
 */
DocumentApp.Footnote.prototype.setAttributes = function(attributes) {};

/** @constructor */
DocumentApp.FootnoteSection = function() {};

/**
 * @param {DocumentApp.Paragraph|string} paragraphOrText
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.FootnoteSection.prototype.appendParagraph = function(paragraphOrText) {};

/**
 * @return {DocumentApp.FootnoteSection}
 */
DocumentApp.FootnoteSection.prototype.clear = function() {};

/**
 * @return {DocumentApp.FootnoteSection}
 */
DocumentApp.FootnoteSection.prototype.copy = function() {};

/**
 * @return {DocumentApp.Text}
 */
DocumentApp.FootnoteSection.prototype.editAsText = function() {};

/**
 * @param {DocumentApp.ElementType} elementType
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.FootnoteSection.prototype.findElement = function(elementType, opt_from) {};

/**
 * @param {string} searchPattern
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.FootnoteSection.prototype.findText = function(searchPattern, opt_from) {};

/**
 * @return {Object}
 */
DocumentApp.FootnoteSection.prototype.getAttributes = function() {};

/**
 * @param {number} childIndex
 * @return {DocumentApp.Element}
 */
DocumentApp.FootnoteSection.prototype.getChild = function(childIndex) {};

/**
 * @param {DocumentApp.Element} child
 * @return {number}
 */
DocumentApp.FootnoteSection.prototype.getChildIndex = function(child) {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.FootnoteSection.prototype.getNextSibling = function() {};

/**
 * @return {number}
 */
DocumentApp.FootnoteSection.prototype.getNumChildren = function() {};

/**
 * @return {Array<DocumentApp.Paragraph>}
 */
DocumentApp.FootnoteSection.prototype.getParagraphs = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.FootnoteSection.prototype.getParent = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.FootnoteSection.prototype.getPreviousSibling = function() {};

/**
 * @return {string}
 */
DocumentApp.FootnoteSection.prototype.getText = function() {};

/**
 * @return {DocumentApp.TextAlignment}
 */
DocumentApp.FootnoteSection.prototype.getTextAlignment = function() {};

/**
 * @return {DocumentApp.ElementType}
 */
DocumentApp.FootnoteSection.prototype.getType = function() {};

/**
 * @param {number} childIndex
 * @param {DocumentApp.Paragraph|string} paragraphOrText
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.FootnoteSection.prototype.insertParagraph = function(childIndex, paragraphOrText) {};

/**
 * @param {DocumentApp.Element} child
 * @return {DocumentApp.FootnoteSection}
 */
DocumentApp.FootnoteSection.prototype.removeChild = function(child) {};

/**
 * @return {DocumentApp.FootnoteSection}
 */
DocumentApp.FootnoteSection.prototype.removeFromParent = function() {};

/**
 * @param {string} searchPattern
 * @param {string} replacement
 * @return {DocumentApp.Element}
 */
DocumentApp.FootnoteSection.prototype.replaceText = function(searchPattern, replacement) {};

/**
 * @param {Object} attributes
 * @return {DocumentApp.FootnoteSection}
 */
DocumentApp.FootnoteSection.prototype.setAttributes = function(attributes) {};

/**
 * @param {string} text
 * @return {DocumentApp.FootnoteSection}
 */
DocumentApp.FootnoteSection.prototype.setText = function(text) {};

/**
 * @param {DocumentApp.TextAlignment} textAlignment
 * @return {DocumentApp.FootnoteSection}
 */
DocumentApp.FootnoteSection.prototype.setTextAlignment = function(textAlignment) {};

/** @enum {string} */
DocumentApp.GlyphType = {
  BULLET: '',
  HOLLOW_BULLET: '',
  LATIN_LOWER: '',
  LATIN_UPPER: '',
  NUMBER: '',
  ROMAN_LOWER: '',
  ROMAN_UPPER: '',
  SQUARE_BULLET: ''
};

/** @constructor */
DocumentApp.HeaderSection = function() {};

/**
 * @return {DocumentApp.HorizontalRule}
 */
DocumentApp.HeaderSection.prototype.appendHorizontalRule = function() {};

/**
 * @param {BlobSource|DocumentApp.InlineImage} image
 * @return {DocumentApp.InlineImage}
 */
DocumentApp.HeaderSection.prototype.appendImage = function(image) {};

/**
 * @param {DocumentApp.ListItem|string} listItemOrText
 * @return {DocumentApp.ListItem}
 */
DocumentApp.HeaderSection.prototype.appendListItem = function(listItemOrText) {};

/**
 * @param {DocumentApp.Paragraph|string} paragraphOrText
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.HeaderSection.prototype.appendParagraph = function(paragraphOrText) {};

/**
 * @param {Array<Array<string>>|DocumentApp.Table|undefined} opt_cellsOrTable
 * @return {DocumentApp.Table}
 */
DocumentApp.HeaderSection.prototype.appendTable = function(opt_cellsOrTable) {};

/**
 * @return {DocumentApp.HeaderSection}
 */
DocumentApp.HeaderSection.prototype.clear = function() {};

/**
 * @return {DocumentApp.HeaderSection}
 */
DocumentApp.HeaderSection.prototype.copy = function() {};

/**
 * @return {DocumentApp.Text}
 */
DocumentApp.HeaderSection.prototype.editAsText = function() {};

/**
 * @param {DocumentApp.ElementType} elementType
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.HeaderSection.prototype.findElement = function(elementType, opt_from) {};

/**
 * @param {string} searchPattern
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.HeaderSection.prototype.findText = function(searchPattern, opt_from) {};

/**
 * @return {Object}
 */
DocumentApp.HeaderSection.prototype.getAttributes = function() {};

/**
 * @param {number} childIndex
 * @return {DocumentApp.Element}
 */
DocumentApp.HeaderSection.prototype.getChild = function(childIndex) {};

/**
 * @param {DocumentApp.Element} child
 * @return {number}
 */
DocumentApp.HeaderSection.prototype.getChildIndex = function(child) {};

/**
 * @return {Array<DocumentApp.InlineImage>}
 */
DocumentApp.HeaderSection.prototype.getImages = function() {};

/**
 * @return {Array<DocumentApp.ListItem>}
 */
DocumentApp.HeaderSection.prototype.getListItems = function() {};

/**
 * @return {number}
 */
DocumentApp.HeaderSection.prototype.getNumChildren = function() {};

/**
 * @return {Array<DocumentApp.Paragraph>}
 */
DocumentApp.HeaderSection.prototype.getParagraphs = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.HeaderSection.prototype.getParent = function() {};

/**
 * @return {Array<DocumentApp.Table>}
 */
DocumentApp.HeaderSection.prototype.getTables = function() {};

/**
 * @return {string}
 */
DocumentApp.HeaderSection.prototype.getText = function() {};

/**
 * @return {DocumentApp.TextAlignment}
 */
DocumentApp.HeaderSection.prototype.getTextAlignment = function() {};

/**
 * @return {DocumentApp.ElementType}
 */
DocumentApp.HeaderSection.prototype.getType = function() {};

/**
 * @param {number} childIndex
 * @return {DocumentApp.HorizontalRule}
 */
DocumentApp.HeaderSection.prototype.insertHorizontalRule = function(childIndex) {};

/**
 * @param {number} childIndex
 * @param {BlobSource|DocumentApp.InlineImage} image
 * @return {DocumentApp.InlineImage}
 */
DocumentApp.HeaderSection.prototype.insertImage = function(childIndex, image) {};

/**
 * @param {number} childIndex
 * @param {DocumentApp.ListItem|string} listItemOrText
 * @return {DocumentApp.ListItem}
 */
DocumentApp.HeaderSection.prototype.insertListItem = function(childIndex, listItemOrText) {};

/**
 * @param {number} childIndex
 * @param {DocumentApp.Paragraph|string} paragraphOrText
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.HeaderSection.prototype.insertParagraph = function(childIndex, paragraphOrText) {};

/**
 * @param {number} childIndex
 * @param {Array<Array<string>>|DocumentApp.Table|undefined} opt_cellsOrTable
 * @return {DocumentApp.Table}
 */
DocumentApp.HeaderSection.prototype.insertTable = function(childIndex, opt_cellsOrTable) {};

/**
 * @param {DocumentApp.Element} child
 * @return {DocumentApp.HeaderSection}
 */
DocumentApp.HeaderSection.prototype.removeChild = function(child) {};

/**
 * @return {DocumentApp.HeaderSection}
 */
DocumentApp.HeaderSection.prototype.removeFromParent = function() {};

/**
 * @param {string} searchPattern
 * @param {string} replacement
 * @return {DocumentApp.Element}
 */
DocumentApp.HeaderSection.prototype.replaceText = function(searchPattern, replacement) {};

/**
 * @param {Object} attributes
 * @return {DocumentApp.HeaderSection}
 */
DocumentApp.HeaderSection.prototype.setAttributes = function(attributes) {};

/**
 * @param {string} text
 * @return {DocumentApp.HeaderSection}
 */
DocumentApp.HeaderSection.prototype.setText = function(text) {};

/**
 * @param {DocumentApp.TextAlignment} textAlignment
 * @return {DocumentApp.HeaderSection}
 */
DocumentApp.HeaderSection.prototype.setTextAlignment = function(textAlignment) {};

/** @enum {string} */
DocumentApp.HorizontalAlignment = {
  CENTER: '',
  JUSTIFY: '',
  LEFT: '',
  RIGHT: ''
};

/** @constructor */
DocumentApp.HorizontalRule = function() {};

/**
 * @return {DocumentApp.HorizontalRule}
 */
DocumentApp.HorizontalRule.prototype.copy = function() {};

/**
 * @return {Object}
 */
DocumentApp.HorizontalRule.prototype.getAttributes = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.HorizontalRule.prototype.getNextSibling = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.HorizontalRule.prototype.getParent = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.HorizontalRule.prototype.getPreviousSibling = function() {};

/**
 * @return {DocumentApp.ElementType}
 */
DocumentApp.HorizontalRule.prototype.getType = function() {};

/**
 * @return {boolean}
 */
DocumentApp.HorizontalRule.prototype.isAtDocumentEnd = function() {};

/**
 * @return {DocumentApp.HorizontalRule}
 */
DocumentApp.HorizontalRule.prototype.removeFromParent = function() {};

/**
 * @param {Object} attributes
 * @return {DocumentApp.HorizontalRule}
 */
DocumentApp.HorizontalRule.prototype.setAttributes = function(attributes) {};

/** @constructor */
DocumentApp.InlineDrawing = function() {};

/**
 * @return {DocumentApp.InlineDrawing}
 */
DocumentApp.InlineDrawing.prototype.copy = function() {};

/**
 * @return {string}
 */
DocumentApp.InlineDrawing.prototype.getAltDescription = function() {};

/**
 * @return {string}
 */
DocumentApp.InlineDrawing.prototype.getAltTitle = function() {};

/**
 * @return {Object}
 */
DocumentApp.InlineDrawing.prototype.getAttributes = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.InlineDrawing.prototype.getNextSibling = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.InlineDrawing.prototype.getParent = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.InlineDrawing.prototype.getPreviousSibling = function() {};

/**
 * @return {DocumentApp.ElementType}
 */
DocumentApp.InlineDrawing.prototype.getType = function() {};

/**
 * @return {boolean}
 */
DocumentApp.InlineDrawing.prototype.isAtDocumentEnd = function() {};

/**
 * @return {DocumentApp.InlineDrawing}
 */
DocumentApp.InlineDrawing.prototype.merge = function() {};

/**
 * @return {DocumentApp.InlineDrawing}
 */
DocumentApp.InlineDrawing.prototype.removeFromParent = function() {};

/**
 * @param {string} description
 * @return {DocumentApp.InlineDrawing}
 */
DocumentApp.InlineDrawing.prototype.setAltDescription = function(description) {};

/**
 * @param {string} title
 * @return {DocumentApp.InlineDrawing}
 */
DocumentApp.InlineDrawing.prototype.setAltTitle = function(title) {};

/**
 * @param {Object} attributes
 * @return {DocumentApp.InlineDrawing}
 */
DocumentApp.InlineDrawing.prototype.setAttributes = function(attributes) {};

/** @constructor */
DocumentApp.InlineImage = function() {};

/**
 * @return {DocumentApp.InlineImage}
 */
DocumentApp.InlineImage.prototype.copy = function() {};

/**
 * @return {string}
 */
DocumentApp.InlineImage.prototype.getAltDescription = function() {};

/**
 * @return {string}
 */
DocumentApp.InlineImage.prototype.getAltTitle = function() {};

/**
 * @param {string} contentType
 * @return {Blob}
 */
DocumentApp.InlineImage.prototype.getAs = function(contentType) {};

/**
 * @return {Object}
 */
DocumentApp.InlineImage.prototype.getAttributes = function() {};

/**
 * @return {Blob}
 */
DocumentApp.InlineImage.prototype.getBlob = function() {};

/**
 * @return {number}
 */
DocumentApp.InlineImage.prototype.getHeight = function() {};

/**
 * @return {string}
 */
DocumentApp.InlineImage.prototype.getLinkUrl = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.InlineImage.prototype.getNextSibling = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.InlineImage.prototype.getParent = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.InlineImage.prototype.getPreviousSibling = function() {};

/**
 * @return {DocumentApp.ElementType}
 */
DocumentApp.InlineImage.prototype.getType = function() {};

/**
 * @return {number}
 */
DocumentApp.InlineImage.prototype.getWidth = function() {};

/**
 * @return {boolean}
 */
DocumentApp.InlineImage.prototype.isAtDocumentEnd = function() {};

/**
 * @return {DocumentApp.InlineImage}
 */
DocumentApp.InlineImage.prototype.merge = function() {};

/**
 * @return {DocumentApp.InlineImage}
 */
DocumentApp.InlineImage.prototype.removeFromParent = function() {};

/**
 * @param {string} description
 * @return {DocumentApp.InlineImage}
 */
DocumentApp.InlineImage.prototype.setAltDescription = function(description) {};

/**
 * @param {string} title
 * @return {DocumentApp.InlineImage}
 */
DocumentApp.InlineImage.prototype.setAltTitle = function(title) {};

/**
 * @param {Object} attributes
 * @return {DocumentApp.InlineImage}
 */
DocumentApp.InlineImage.prototype.setAttributes = function(attributes) {};

/**
 * @param {number} height
 * @return {DocumentApp.InlineImage}
 */
DocumentApp.InlineImage.prototype.setHeight = function(height) {};

/**
 * @param {string} url
 * @return {DocumentApp.InlineImage}
 */
DocumentApp.InlineImage.prototype.setLinkUrl = function(url) {};

/**
 * @param {number} width
 * @return {DocumentApp.InlineImage}
 */
DocumentApp.InlineImage.prototype.setWidth = function(width) {};

/** @constructor */
DocumentApp.ListItem = function() {};

/**
 * @param {BlobSource} image
 * @return {DocumentApp.PositionedImage}
 */
DocumentApp.ListItem.prototype.addPositionedImage = function(image) {};

/**
 * @return {DocumentApp.HorizontalRule}
 */
DocumentApp.ListItem.prototype.appendHorizontalRule = function() {};

/**
 * @param {BlobSource|DocumentApp.InlineImage} image
 * @return {DocumentApp.InlineImage}
 */
DocumentApp.ListItem.prototype.appendInlineImage = function(image) {};

/**
 * @param {DocumentApp.PageBreak=} opt_pageBreak
 * @return {DocumentApp.PageBreak}
 */
DocumentApp.ListItem.prototype.appendPageBreak = function(opt_pageBreak) {};

/**
 * @param {DocumentApp.Text|string} text
 * @return {DocumentApp.Text}
 */
DocumentApp.ListItem.prototype.appendText = function(text) {};

/**
 * @return {DocumentApp.ListItem}
 */
DocumentApp.ListItem.prototype.clear = function() {};

/**
 * @return {DocumentApp.ListItem}
 */
DocumentApp.ListItem.prototype.copy = function() {};

/**
 * @return {DocumentApp.Text}
 */
DocumentApp.ListItem.prototype.editAsText = function() {};

/**
 * @param {DocumentApp.ElementType} elementType
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.ListItem.prototype.findElement = function(elementType, opt_from) {};

/**
 * @param {string} searchPattern
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.ListItem.prototype.findText = function(searchPattern, opt_from) {};

/**
 * @return {DocumentApp.HorizontalAlignment}
 */
DocumentApp.ListItem.prototype.getAlignment = function() {};

/**
 * @return {Object}
 */
DocumentApp.ListItem.prototype.getAttributes = function() {};

/**
 * @param {number} childIndex
 * @return {DocumentApp.Element}
 */
DocumentApp.ListItem.prototype.getChild = function(childIndex) {};

/**
 * @param {DocumentApp.Element} child
 * @return {number}
 */
DocumentApp.ListItem.prototype.getChildIndex = function(child) {};

/**
 * @return {DocumentApp.GlyphType}
 */
DocumentApp.ListItem.prototype.getGlyphType = function() {};

/**
 * @return {DocumentApp.ParagraphHeading}
 */
DocumentApp.ListItem.prototype.getHeading = function() {};

/**
 * @return {number}
 */
DocumentApp.ListItem.prototype.getIndentEnd = function() {};

/**
 * @return {number}
 */
DocumentApp.ListItem.prototype.getIndentFirstLine = function() {};

/**
 * @return {number}
 */
DocumentApp.ListItem.prototype.getIndentStart = function() {};

/**
 * @return {number}
 */
DocumentApp.ListItem.prototype.getLineSpacing = function() {};

/**
 * @return {string}
 */
DocumentApp.ListItem.prototype.getLinkUrl = function() {};

/**
 * @return {string}
 */
DocumentApp.ListItem.prototype.getListId = function() {};

/**
 * @return {number}
 */
DocumentApp.ListItem.prototype.getNestingLevel = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.ListItem.prototype.getNextSibling = function() {};

/**
 * @return {number}
 */
DocumentApp.ListItem.prototype.getNumChildren = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.ListItem.prototype.getParent = function() {};

/**
 * @param {string} id
 * @return {DocumentApp.PositionedImage}
 */
DocumentApp.ListItem.prototype.getPositionedImage = function(id) {};

/**
 * @return {Array<DocumentApp.PositionedImage>}
 */
DocumentApp.ListItem.prototype.getPositionedImages = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.ListItem.prototype.getPreviousSibling = function() {};

/**
 * @return {number}
 */
DocumentApp.ListItem.prototype.getSpacingAfter = function() {};

/**
 * @return {number}
 */
DocumentApp.ListItem.prototype.getSpacingBefore = function() {};

/**
 * @return {string}
 */
DocumentApp.ListItem.prototype.getText = function() {};

/**
 * @return {DocumentApp.TextAlignment}
 */
DocumentApp.ListItem.prototype.getTextAlignment = function() {};

/**
 * @return {DocumentApp.ElementType}
 */
DocumentApp.ListItem.prototype.getType = function() {};

/**
 * @param {number} childIndex
 * @return {DocumentApp.HorizontalRule}
 */
DocumentApp.ListItem.prototype.insertHorizontalRule = function(childIndex) {};

/**
 * @param {number} childIndex
 * @param {BlobSource|DocumentApp.InlineImage} image
 * @return {DocumentApp.InlineImage}
 */
DocumentApp.ListItem.prototype.insertInlineImage = function(childIndex, image) {};

/**
 * @param {number} childIndex
 * @param {DocumentApp.PageBreak=} opt_pageBreak
 * @return {DocumentApp.PageBreak}
 */
DocumentApp.ListItem.prototype.insertPageBreak = function(childIndex, opt_pageBreak) {};

/**
 * @param {number} childIndex
 * @param {DocumentApp.Text|string} text
 * @return {DocumentApp.Text}
 */
DocumentApp.ListItem.prototype.insertText = function(childIndex, text) {};

/**
 * @return {boolean}
 */
DocumentApp.ListItem.prototype.isAtDocumentEnd = function() {};

/**
 * @return {boolean}
 */
DocumentApp.ListItem.prototype.isLeftToRight = function() {};

/**
 * @return {DocumentApp.ListItem}
 */
DocumentApp.ListItem.prototype.merge = function() {};

/**
 * @param {DocumentApp.Element} child
 * @return {DocumentApp.ListItem}
 */
DocumentApp.ListItem.prototype.removeChild = function(child) {};

/**
 * @return {DocumentApp.ListItem}
 */
DocumentApp.ListItem.prototype.removeFromParent = function() {};

/**
 * @param {string} id
 * @return {boolean}
 */
DocumentApp.ListItem.prototype.removePositionedImage = function(id) {};

/**
 * @param {string} searchPattern
 * @param {string} replacement
 * @return {DocumentApp.Element}
 */
DocumentApp.ListItem.prototype.replaceText = function(searchPattern, replacement) {};

/**
 * @param {DocumentApp.HorizontalAlignment} alignment
 * @return {DocumentApp.ListItem}
 */
DocumentApp.ListItem.prototype.setAlignment = function(alignment) {};

/**
 * @param {Object} attributes
 * @return {DocumentApp.ListItem}
 */
DocumentApp.ListItem.prototype.setAttributes = function(attributes) {};

/**
 * @param {DocumentApp.GlyphType} glyphType
 * @return {DocumentApp.ListItem}
 */
DocumentApp.ListItem.prototype.setGlyphType = function(glyphType) {};

/**
 * @param {DocumentApp.ParagraphHeading} heading
 * @return {DocumentApp.ListItem}
 */
DocumentApp.ListItem.prototype.setHeading = function(heading) {};

/**
 * @param {number} indentEnd
 * @return {DocumentApp.ListItem}
 */
DocumentApp.ListItem.prototype.setIndentEnd = function(indentEnd) {};

/**
 * @param {number} indentFirstLine
 * @return {DocumentApp.ListItem}
 */
DocumentApp.ListItem.prototype.setIndentFirstLine = function(indentFirstLine) {};

/**
 * @param {number} indentStart
 * @return {DocumentApp.ListItem}
 */
DocumentApp.ListItem.prototype.setIndentStart = function(indentStart) {};

/**
 * @param {boolean} leftToRight
 * @return {DocumentApp.ListItem}
 */
DocumentApp.ListItem.prototype.setLeftToRight = function(leftToRight) {};

/**
 * @param {number} multiplier
 * @return {DocumentApp.ListItem}
 */
DocumentApp.ListItem.prototype.setLineSpacing = function(multiplier) {};

/**
 * @param {string} url
 * @return {DocumentApp.ListItem}
 */
DocumentApp.ListItem.prototype.setLinkUrl = function(url) {};

/**
 * @param {DocumentApp.ListItem} listItem
 * @return {DocumentApp.ListItem}
 */
DocumentApp.ListItem.prototype.setListId = function(listItem) {};

/**
 * @param {number} nestingLevel
 * @return {DocumentApp.ListItem}
 */
DocumentApp.ListItem.prototype.setNestingLevel = function(nestingLevel) {};

/**
 * @param {number} spacingAfter
 * @return {DocumentApp.ListItem}
 */
DocumentApp.ListItem.prototype.setSpacingAfter = function(spacingAfter) {};

/**
 * @param {number} spacingBefore
 * @return {DocumentApp.ListItem}
 */
DocumentApp.ListItem.prototype.setSpacingBefore = function(spacingBefore) {};

/**
 * @param {string} text
 */
DocumentApp.ListItem.prototype.setText = function(text) {};

/**
 * @param {DocumentApp.TextAlignment} textAlignment
 * @return {DocumentApp.ListItem}
 */
DocumentApp.ListItem.prototype.setTextAlignment = function(textAlignment) {};

/** @constructor */
DocumentApp.NamedRange = function() {};

/**
 * @return {string}
 */
DocumentApp.NamedRange.prototype.getId = function() {};

/**
 * @return {string}
 */
DocumentApp.NamedRange.prototype.getName = function() {};

/**
 * @return {DocumentApp.Range}
 */
DocumentApp.NamedRange.prototype.getRange = function() {};

/**
 */
DocumentApp.NamedRange.prototype.remove = function() {};

/** @constructor */
DocumentApp.PageBreak = function() {};

/**
 * @return {DocumentApp.PageBreak}
 */
DocumentApp.PageBreak.prototype.copy = function() {};

/**
 * @return {Object}
 */
DocumentApp.PageBreak.prototype.getAttributes = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.PageBreak.prototype.getNextSibling = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.PageBreak.prototype.getParent = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.PageBreak.prototype.getPreviousSibling = function() {};

/**
 * @return {DocumentApp.ElementType}
 */
DocumentApp.PageBreak.prototype.getType = function() {};

/**
 * @return {boolean}
 */
DocumentApp.PageBreak.prototype.isAtDocumentEnd = function() {};

/**
 * @return {DocumentApp.PageBreak}
 */
DocumentApp.PageBreak.prototype.removeFromParent = function() {};

/**
 * @param {Object} attributes
 * @return {DocumentApp.PageBreak}
 */
DocumentApp.PageBreak.prototype.setAttributes = function(attributes) {};

/** @constructor */
DocumentApp.Paragraph = function() {};

/**
 * @param {BlobSource} image
 * @return {DocumentApp.PositionedImage}
 */
DocumentApp.Paragraph.prototype.addPositionedImage = function(image) {};

/**
 * @return {DocumentApp.HorizontalRule}
 */
DocumentApp.Paragraph.prototype.appendHorizontalRule = function() {};

/**
 * @param {BlobSource|DocumentApp.InlineImage} image
 * @return {DocumentApp.InlineImage}
 */
DocumentApp.Paragraph.prototype.appendInlineImage = function(image) {};

/**
 * @param {DocumentApp.PageBreak=} opt_pageBreak
 * @return {DocumentApp.PageBreak}
 */
DocumentApp.Paragraph.prototype.appendPageBreak = function(opt_pageBreak) {};

/**
 * @param {DocumentApp.Text|string} text
 * @return {DocumentApp.Text}
 */
DocumentApp.Paragraph.prototype.appendText = function(text) {};

/**
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.Paragraph.prototype.clear = function() {};

/**
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.Paragraph.prototype.copy = function() {};

/**
 * @return {DocumentApp.Text}
 */
DocumentApp.Paragraph.prototype.editAsText = function() {};

/**
 * @param {DocumentApp.ElementType} elementType
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.Paragraph.prototype.findElement = function(elementType, opt_from) {};

/**
 * @param {string} searchPattern
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.Paragraph.prototype.findText = function(searchPattern, opt_from) {};

/**
 * @return {DocumentApp.HorizontalAlignment}
 */
DocumentApp.Paragraph.prototype.getAlignment = function() {};

/**
 * @return {Object}
 */
DocumentApp.Paragraph.prototype.getAttributes = function() {};

/**
 * @param {number} childIndex
 * @return {DocumentApp.Element}
 */
DocumentApp.Paragraph.prototype.getChild = function(childIndex) {};

/**
 * @param {DocumentApp.Element} child
 * @return {number}
 */
DocumentApp.Paragraph.prototype.getChildIndex = function(child) {};

/**
 * @return {DocumentApp.ParagraphHeading}
 */
DocumentApp.Paragraph.prototype.getHeading = function() {};

/**
 * @return {number}
 */
DocumentApp.Paragraph.prototype.getIndentEnd = function() {};

/**
 * @return {number}
 */
DocumentApp.Paragraph.prototype.getIndentFirstLine = function() {};

/**
 * @return {number}
 */
DocumentApp.Paragraph.prototype.getIndentStart = function() {};

/**
 * @return {number}
 */
DocumentApp.Paragraph.prototype.getLineSpacing = function() {};

/**
 * @return {string}
 */
DocumentApp.Paragraph.prototype.getLinkUrl = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.Paragraph.prototype.getNextSibling = function() {};

/**
 * @return {number}
 */
DocumentApp.Paragraph.prototype.getNumChildren = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.Paragraph.prototype.getParent = function() {};

/**
 * @param {string} id
 * @return {DocumentApp.PositionedImage}
 */
DocumentApp.Paragraph.prototype.getPositionedImage = function(id) {};

/**
 * @return {Array<DocumentApp.PositionedImage>}
 */
DocumentApp.Paragraph.prototype.getPositionedImages = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.Paragraph.prototype.getPreviousSibling = function() {};

/**
 * @return {number}
 */
DocumentApp.Paragraph.prototype.getSpacingAfter = function() {};

/**
 * @return {number}
 */
DocumentApp.Paragraph.prototype.getSpacingBefore = function() {};

/**
 * @return {string}
 */
DocumentApp.Paragraph.prototype.getText = function() {};

/**
 * @return {DocumentApp.TextAlignment}
 */
DocumentApp.Paragraph.prototype.getTextAlignment = function() {};

/**
 * @return {DocumentApp.ElementType}
 */
DocumentApp.Paragraph.prototype.getType = function() {};

/**
 * @param {number} childIndex
 * @return {DocumentApp.HorizontalRule}
 */
DocumentApp.Paragraph.prototype.insertHorizontalRule = function(childIndex) {};

/**
 * @param {number} childIndex
 * @param {BlobSource|DocumentApp.InlineImage} image
 * @return {DocumentApp.InlineImage}
 */
DocumentApp.Paragraph.prototype.insertInlineImage = function(childIndex, image) {};

/**
 * @param {number} childIndex
 * @param {DocumentApp.PageBreak=} opt_pageBreak
 * @return {DocumentApp.PageBreak}
 */
DocumentApp.Paragraph.prototype.insertPageBreak = function(childIndex, opt_pageBreak) {};

/**
 * @param {number} childIndex
 * @param {DocumentApp.Text|string} text
 * @return {DocumentApp.Text}
 */
DocumentApp.Paragraph.prototype.insertText = function(childIndex, text) {};

/**
 * @return {boolean}
 */
DocumentApp.Paragraph.prototype.isAtDocumentEnd = function() {};

/**
 * @return {boolean}
 */
DocumentApp.Paragraph.prototype.isLeftToRight = function() {};

/**
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.Paragraph.prototype.merge = function() {};

/**
 * @param {DocumentApp.Element} child
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.Paragraph.prototype.removeChild = function(child) {};

/**
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.Paragraph.prototype.removeFromParent = function() {};

/**
 * @param {string} id
 * @return {boolean}
 */
DocumentApp.Paragraph.prototype.removePositionedImage = function(id) {};

/**
 * @param {string} searchPattern
 * @param {string} replacement
 * @return {DocumentApp.Element}
 */
DocumentApp.Paragraph.prototype.replaceText = function(searchPattern, replacement) {};

/**
 * @param {DocumentApp.HorizontalAlignment} alignment
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.Paragraph.prototype.setAlignment = function(alignment) {};

/**
 * @param {Object} attributes
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.Paragraph.prototype.setAttributes = function(attributes) {};

/**
 * @param {DocumentApp.ParagraphHeading} heading
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.Paragraph.prototype.setHeading = function(heading) {};

/**
 * @param {number} indentEnd
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.Paragraph.prototype.setIndentEnd = function(indentEnd) {};

/**
 * @param {number} indentFirstLine
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.Paragraph.prototype.setIndentFirstLine = function(indentFirstLine) {};

/**
 * @param {number} indentStart
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.Paragraph.prototype.setIndentStart = function(indentStart) {};

/**
 * @param {boolean} leftToRight
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.Paragraph.prototype.setLeftToRight = function(leftToRight) {};

/**
 * @param {number} multiplier
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.Paragraph.prototype.setLineSpacing = function(multiplier) {};

/**
 * @param {string} url
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.Paragraph.prototype.setLinkUrl = function(url) {};

/**
 * @param {number} spacingAfter
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.Paragraph.prototype.setSpacingAfter = function(spacingAfter) {};

/**
 * @param {number} spacingBefore
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.Paragraph.prototype.setSpacingBefore = function(spacingBefore) {};

/**
 * @param {string} text
 */
DocumentApp.Paragraph.prototype.setText = function(text) {};

/**
 * @param {DocumentApp.TextAlignment} textAlignment
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.Paragraph.prototype.setTextAlignment = function(textAlignment) {};

/** @enum {string} */
DocumentApp.ParagraphHeading = {
  HEADING1: '',
  HEADING2: '',
  HEADING3: '',
  HEADING4: '',
  HEADING5: '',
  HEADING6: '',
  NORMAL: '',
  SUBTITLE: '',
  TITLE: ''
};

/** @constructor */
DocumentApp.Person = function() {};

/**
 * @return {DocumentApp.Person}
 */
DocumentApp.Person.prototype.copy = function() {};

/**
 * @return {Object}
 */
DocumentApp.Person.prototype.getAttributes = function() {};

/**
 * @return {string}
 */
DocumentApp.Person.prototype.getEmail = function() {};

/**
 * @return {string}
 */
DocumentApp.Person.prototype.getName = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.Person.prototype.getNextSibling = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.Person.prototype.getParent = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.Person.prototype.getPreviousSibling = function() {};

/**
 * @return {DocumentApp.ElementType}
 */
DocumentApp.Person.prototype.getType = function() {};

/**
 * @return {boolean}
 */
DocumentApp.Person.prototype.isAtDocumentEnd = function() {};

/**
 * @return {DocumentApp.Person}
 */
DocumentApp.Person.prototype.merge = function() {};

/**
 * @return {DocumentApp.Person}
 */
DocumentApp.Person.prototype.removeFromParent = function() {};

/**
 * @param {Object} attributes
 * @return {DocumentApp.Person}
 */
DocumentApp.Person.prototype.setAttributes = function(attributes) {};

/** @constructor */
DocumentApp.Position = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.Position.prototype.getElement = function() {};

/**
 * @return {number}
 */
DocumentApp.Position.prototype.getOffset = function() {};

/**
 * @return {DocumentApp.Text}
 */
DocumentApp.Position.prototype.getSurroundingText = function() {};

/**
 * @return {number}
 */
DocumentApp.Position.prototype.getSurroundingTextOffset = function() {};

/**
 * @return {DocumentApp.Bookmark}
 */
DocumentApp.Position.prototype.insertBookmark = function() {};

/**
 * @param {BlobSource} image
 * @return {DocumentApp.InlineImage}
 */
DocumentApp.Position.prototype.insertInlineImage = function(image) {};

/**
 * @param {string} text
 * @return {DocumentApp.Text}
 */
DocumentApp.Position.prototype.insertText = function(text) {};

/** @constructor */
DocumentApp.PositionedImage = function() {};

/**
 * @param {string} contentType
 * @return {Blob}
 */
DocumentApp.PositionedImage.prototype.getAs = function(contentType) {};

/**
 * @return {Blob}
 */
DocumentApp.PositionedImage.prototype.getBlob = function() {};

/**
 * @return {number}
 */
DocumentApp.PositionedImage.prototype.getHeight = function() {};

/**
 * @return {string}
 */
DocumentApp.PositionedImage.prototype.getId = function() {};

/**
 * @return {DocumentApp.PositionedLayout}
 */
DocumentApp.PositionedImage.prototype.getLayout = function() {};

/**
 * @return {number}
 */
DocumentApp.PositionedImage.prototype.getLeftOffset = function() {};

/**
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.PositionedImage.prototype.getParagraph = function() {};

/**
 * @return {number}
 */
DocumentApp.PositionedImage.prototype.getTopOffset = function() {};

/**
 * @return {number}
 */
DocumentApp.PositionedImage.prototype.getWidth = function() {};

/**
 * @param {number} height
 * @return {DocumentApp.PositionedImage}
 */
DocumentApp.PositionedImage.prototype.setHeight = function(height) {};

/**
 * @param {DocumentApp.PositionedLayout} layout
 * @return {DocumentApp.PositionedImage}
 */
DocumentApp.PositionedImage.prototype.setLayout = function(layout) {};

/**
 * @param {number} offset
 * @return {DocumentApp.PositionedImage}
 */
DocumentApp.PositionedImage.prototype.setLeftOffset = function(offset) {};

/**
 * @param {number} offset
 * @return {DocumentApp.PositionedImage}
 */
DocumentApp.PositionedImage.prototype.setTopOffset = function(offset) {};

/**
 * @param {number} width
 * @return {DocumentApp.PositionedImage}
 */
DocumentApp.PositionedImage.prototype.setWidth = function(width) {};

/** @enum {string} */
DocumentApp.PositionedLayout = {
  ABOVE_TEXT: '',
  BREAK_BOTH: '',
  BREAK_LEFT: '',
  BREAK_RIGHT: '',
  WRAP_TEXT: ''
};

/** @constructor */
DocumentApp.Range = function() {};

/**
 * @return {Array<DocumentApp.RangeElement>}
 */
DocumentApp.Range.prototype.getRangeElements = function() {};

/** @constructor */
DocumentApp.RangeBuilder = function() {};

/**
 * @param {DocumentApp.Element|DocumentApp.Text} elementOrTextElement
 * @param {number=} opt_startOffset
 * @param {number=} opt_endOffsetInclusive
 * @return {DocumentApp.RangeBuilder}
 */
DocumentApp.RangeBuilder.prototype.addElement = function(elementOrTextElement, opt_startOffset, opt_endOffsetInclusive) {};

/**
 * @param {DocumentApp.Element|DocumentApp.Text} startElementOrStartTextElement
 * @param {DocumentApp.Element|number} endElementInclusiveOrStartOffset
 * @param {DocumentApp.Text=} opt_endTextElementInclusive
 * @param {number=} opt_endOffsetInclusive
 * @return {DocumentApp.RangeBuilder}
 */
DocumentApp.RangeBuilder.prototype.addElementsBetween = function(startElementOrStartTextElement, endElementInclusiveOrStartOffset, opt_endTextElementInclusive, opt_endOffsetInclusive) {};

/**
 * @param {DocumentApp.Range} range
 * @return {DocumentApp.RangeBuilder}
 */
DocumentApp.RangeBuilder.prototype.addRange = function(range) {};

/**
 * @return {DocumentApp.Range}
 */
DocumentApp.RangeBuilder.prototype.build = function() {};

/**
 * @return {Array<DocumentApp.RangeElement>}
 */
DocumentApp.RangeBuilder.prototype.getRangeElements = function() {};

/** @constructor */
DocumentApp.RangeElement = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.RangeElement.prototype.getElement = function() {};

/**
 * @return {number}
 */
DocumentApp.RangeElement.prototype.getEndOffsetInclusive = function() {};

/**
 * @return {number}
 */
DocumentApp.RangeElement.prototype.getStartOffset = function() {};

/**
 * @return {boolean}
 */
DocumentApp.RangeElement.prototype.isPartial = function() {};

/** @constructor */
DocumentApp.RichLink = function() {};

/**
 * @return {DocumentApp.RichLink}
 */
DocumentApp.RichLink.prototype.copy = function() {};

/**
 * @return {Object}
 */
DocumentApp.RichLink.prototype.getAttributes = function() {};

/**
 * @return {string}
 */
DocumentApp.RichLink.prototype.getMimeType = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.RichLink.prototype.getNextSibling = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.RichLink.prototype.getParent = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.RichLink.prototype.getPreviousSibling = function() {};

/**
 * @return {string}
 */
DocumentApp.RichLink.prototype.getTitle = function() {};

/**
 * @return {DocumentApp.ElementType}
 */
DocumentApp.RichLink.prototype.getType = function() {};

/**
 * @return {string}
 */
DocumentApp.RichLink.prototype.getUrl = function() {};

/**
 * @return {boolean}
 */
DocumentApp.RichLink.prototype.isAtDocumentEnd = function() {};

/**
 * @return {DocumentApp.RichLink}
 */
DocumentApp.RichLink.prototype.merge = function() {};

/**
 * @return {DocumentApp.RichLink}
 */
DocumentApp.RichLink.prototype.removeFromParent = function() {};

/**
 * @param {Object} attributes
 * @return {DocumentApp.RichLink}
 */
DocumentApp.RichLink.prototype.setAttributes = function(attributes) {};

/** @constructor */
DocumentApp.Table = function() {};

/**
 * @param {DocumentApp.TableRow=} opt_tableRow
 * @return {DocumentApp.TableRow}
 */
DocumentApp.Table.prototype.appendTableRow = function(opt_tableRow) {};

/**
 * @return {DocumentApp.Table}
 */
DocumentApp.Table.prototype.clear = function() {};

/**
 * @return {DocumentApp.Table}
 */
DocumentApp.Table.prototype.copy = function() {};

/**
 * @return {DocumentApp.Text}
 */
DocumentApp.Table.prototype.editAsText = function() {};

/**
 * @param {DocumentApp.ElementType} elementType
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.Table.prototype.findElement = function(elementType, opt_from) {};

/**
 * @param {string} searchPattern
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.Table.prototype.findText = function(searchPattern, opt_from) {};

/**
 * @return {Object}
 */
DocumentApp.Table.prototype.getAttributes = function() {};

/**
 * @return {string}
 */
DocumentApp.Table.prototype.getBorderColor = function() {};

/**
 * @return {number}
 */
DocumentApp.Table.prototype.getBorderWidth = function() {};

/**
 * @param {number} rowIndex
 * @param {number} cellIndex
 * @return {DocumentApp.TableCell}
 */
DocumentApp.Table.prototype.getCell = function(rowIndex, cellIndex) {};

/**
 * @param {number} childIndex
 * @return {DocumentApp.Element}
 */
DocumentApp.Table.prototype.getChild = function(childIndex) {};

/**
 * @param {DocumentApp.Element} child
 * @return {number}
 */
DocumentApp.Table.prototype.getChildIndex = function(child) {};

/**
 * @param {number} columnIndex
 * @return {number}
 */
DocumentApp.Table.prototype.getColumnWidth = function(columnIndex) {};

/**
 * @return {string}
 */
DocumentApp.Table.prototype.getLinkUrl = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.Table.prototype.getNextSibling = function() {};

/**
 * @return {number}
 */
DocumentApp.Table.prototype.getNumChildren = function() {};

/**
 * @return {number}
 */
DocumentApp.Table.prototype.getNumRows = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.Table.prototype.getParent = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.Table.prototype.getPreviousSibling = function() {};

/**
 * @param {number} rowIndex
 * @return {DocumentApp.TableRow}
 */
DocumentApp.Table.prototype.getRow = function(rowIndex) {};

/**
 * @return {string}
 */
DocumentApp.Table.prototype.getText = function() {};

/**
 * @return {DocumentApp.TextAlignment}
 */
DocumentApp.Table.prototype.getTextAlignment = function() {};

/**
 * @return {DocumentApp.ElementType}
 */
DocumentApp.Table.prototype.getType = function() {};

/**
 * @param {number} childIndex
 * @param {DocumentApp.TableRow=} opt_tableRow
 * @return {DocumentApp.TableRow}
 */
DocumentApp.Table.prototype.insertTableRow = function(childIndex, opt_tableRow) {};

/**
 * @return {boolean}
 */
DocumentApp.Table.prototype.isAtDocumentEnd = function() {};

/**
 * @param {DocumentApp.Element} child
 * @return {DocumentApp.Table}
 */
DocumentApp.Table.prototype.removeChild = function(child) {};

/**
 * @return {DocumentApp.Table}
 */
DocumentApp.Table.prototype.removeFromParent = function() {};

/**
 * @param {number} rowIndex
 * @return {DocumentApp.TableRow}
 */
DocumentApp.Table.prototype.removeRow = function(rowIndex) {};

/**
 * @param {string} searchPattern
 * @param {string} replacement
 * @return {DocumentApp.Element}
 */
DocumentApp.Table.prototype.replaceText = function(searchPattern, replacement) {};

/**
 * @param {Object} attributes
 * @return {DocumentApp.Table}
 */
DocumentApp.Table.prototype.setAttributes = function(attributes) {};

/**
 * @param {string} color
 * @return {DocumentApp.Table}
 */
DocumentApp.Table.prototype.setBorderColor = function(color) {};

/**
 * @param {number} width
 * @return {DocumentApp.Table}
 */
DocumentApp.Table.prototype.setBorderWidth = function(width) {};

/**
 * @param {number} columnIndex
 * @param {number} width
 * @return {DocumentApp.Table}
 */
DocumentApp.Table.prototype.setColumnWidth = function(columnIndex, width) {};

/**
 * @param {string} url
 * @return {DocumentApp.Table}
 */
DocumentApp.Table.prototype.setLinkUrl = function(url) {};

/**
 * @param {DocumentApp.TextAlignment} textAlignment
 * @return {DocumentApp.Table}
 */
DocumentApp.Table.prototype.setTextAlignment = function(textAlignment) {};

/** @constructor */
DocumentApp.TableCell = function() {};

/**
 * @return {DocumentApp.HorizontalRule}
 */
DocumentApp.TableCell.prototype.appendHorizontalRule = function() {};

/**
 * @param {BlobSource|DocumentApp.InlineImage} image
 * @return {DocumentApp.InlineImage}
 */
DocumentApp.TableCell.prototype.appendImage = function(image) {};

/**
 * @param {DocumentApp.ListItem|string} listItemOrText
 * @return {DocumentApp.ListItem}
 */
DocumentApp.TableCell.prototype.appendListItem = function(listItemOrText) {};

/**
 * @param {DocumentApp.Paragraph|string} paragraphOrText
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.TableCell.prototype.appendParagraph = function(paragraphOrText) {};

/**
 * @param {Array<Array<string>>|DocumentApp.Table|undefined} opt_cellsOrTable
 * @return {DocumentApp.Table}
 */
DocumentApp.TableCell.prototype.appendTable = function(opt_cellsOrTable) {};

/**
 * @return {DocumentApp.TableCell}
 */
DocumentApp.TableCell.prototype.clear = function() {};

/**
 * @return {DocumentApp.TableCell}
 */
DocumentApp.TableCell.prototype.copy = function() {};

/**
 * @return {DocumentApp.Text}
 */
DocumentApp.TableCell.prototype.editAsText = function() {};

/**
 * @param {DocumentApp.ElementType} elementType
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.TableCell.prototype.findElement = function(elementType, opt_from) {};

/**
 * @param {string} searchPattern
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.TableCell.prototype.findText = function(searchPattern, opt_from) {};

/**
 * @return {Object}
 */
DocumentApp.TableCell.prototype.getAttributes = function() {};

/**
 * @return {string}
 */
DocumentApp.TableCell.prototype.getBackgroundColor = function() {};

/**
 * @param {number} childIndex
 * @return {DocumentApp.Element}
 */
DocumentApp.TableCell.prototype.getChild = function(childIndex) {};

/**
 * @param {DocumentApp.Element} child
 * @return {number}
 */
DocumentApp.TableCell.prototype.getChildIndex = function(child) {};

/**
 * @return {number}
 */
DocumentApp.TableCell.prototype.getColSpan = function() {};

/**
 * @return {string}
 */
DocumentApp.TableCell.prototype.getLinkUrl = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.TableCell.prototype.getNextSibling = function() {};

/**
 * @return {number}
 */
DocumentApp.TableCell.prototype.getNumChildren = function() {};

/**
 * @return {number}
 */
DocumentApp.TableCell.prototype.getPaddingBottom = function() {};

/**
 * @return {number}
 */
DocumentApp.TableCell.prototype.getPaddingLeft = function() {};

/**
 * @return {number}
 */
DocumentApp.TableCell.prototype.getPaddingRight = function() {};

/**
 * @return {number}
 */
DocumentApp.TableCell.prototype.getPaddingTop = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.TableCell.prototype.getParent = function() {};

/**
 * @return {DocumentApp.TableRow}
 */
DocumentApp.TableCell.prototype.getParentRow = function() {};

/**
 * @return {DocumentApp.Table}
 */
DocumentApp.TableCell.prototype.getParentTable = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.TableCell.prototype.getPreviousSibling = function() {};

/**
 * @return {number}
 */
DocumentApp.TableCell.prototype.getRowSpan = function() {};

/**
 * @return {string}
 */
DocumentApp.TableCell.prototype.getText = function() {};

/**
 * @return {DocumentApp.TextAlignment}
 */
DocumentApp.TableCell.prototype.getTextAlignment = function() {};

/**
 * @return {DocumentApp.ElementType}
 */
DocumentApp.TableCell.prototype.getType = function() {};

/**
 * @return {DocumentApp.VerticalAlignment}
 */
DocumentApp.TableCell.prototype.getVerticalAlignment = function() {};

/**
 * @return {number}
 */
DocumentApp.TableCell.prototype.getWidth = function() {};

/**
 * @param {number} childIndex
 * @return {DocumentApp.HorizontalRule}
 */
DocumentApp.TableCell.prototype.insertHorizontalRule = function(childIndex) {};

/**
 * @param {number} childIndex
 * @param {BlobSource|DocumentApp.InlineImage} image
 * @return {DocumentApp.InlineImage}
 */
DocumentApp.TableCell.prototype.insertImage = function(childIndex, image) {};

/**
 * @param {number} childIndex
 * @param {DocumentApp.ListItem|string} listItemOrText
 * @return {DocumentApp.ListItem}
 */
DocumentApp.TableCell.prototype.insertListItem = function(childIndex, listItemOrText) {};

/**
 * @param {number} childIndex
 * @param {DocumentApp.Paragraph|string} paragraphOrText
 * @return {DocumentApp.Paragraph}
 */
DocumentApp.TableCell.prototype.insertParagraph = function(childIndex, paragraphOrText) {};

/**
 * @param {number} childIndex
 * @param {Array<Array<string>>|DocumentApp.Table|undefined} opt_cellsOrTable
 * @return {DocumentApp.Table}
 */
DocumentApp.TableCell.prototype.insertTable = function(childIndex, opt_cellsOrTable) {};

/**
 * @return {boolean}
 */
DocumentApp.TableCell.prototype.isAtDocumentEnd = function() {};

/**
 * @return {DocumentApp.TableCell}
 */
DocumentApp.TableCell.prototype.merge = function() {};

/**
 * @param {DocumentApp.Element} child
 * @return {DocumentApp.TableCell}
 */
DocumentApp.TableCell.prototype.removeChild = function(child) {};

/**
 * @return {DocumentApp.TableCell}
 */
DocumentApp.TableCell.prototype.removeFromParent = function() {};

/**
 * @param {string} searchPattern
 * @param {string} replacement
 * @return {DocumentApp.Element}
 */
DocumentApp.TableCell.prototype.replaceText = function(searchPattern, replacement) {};

/**
 * @param {Object} attributes
 * @return {DocumentApp.TableCell}
 */
DocumentApp.TableCell.prototype.setAttributes = function(attributes) {};

/**
 * @param {string} color
 * @return {DocumentApp.TableCell}
 */
DocumentApp.TableCell.prototype.setBackgroundColor = function(color) {};

/**
 * @param {string} url
 * @return {DocumentApp.TableCell}
 */
DocumentApp.TableCell.prototype.setLinkUrl = function(url) {};

/**
 * @param {number} paddingBottom
 * @return {DocumentApp.TableCell}
 */
DocumentApp.TableCell.prototype.setPaddingBottom = function(paddingBottom) {};

/**
 * @param {number} paddingLeft
 * @return {DocumentApp.TableCell}
 */
DocumentApp.TableCell.prototype.setPaddingLeft = function(paddingLeft) {};

/**
 * @param {number} paddingTop
 * @return {DocumentApp.TableCell}
 */
DocumentApp.TableCell.prototype.setPaddingRight = function(paddingTop) {};

/**
 * @param {number} paddingTop
 * @return {DocumentApp.TableCell}
 */
DocumentApp.TableCell.prototype.setPaddingTop = function(paddingTop) {};

/**
 * @param {string} text
 * @return {DocumentApp.TableCell}
 */
DocumentApp.TableCell.prototype.setText = function(text) {};

/**
 * @param {DocumentApp.TextAlignment} textAlignment
 * @return {DocumentApp.TableCell}
 */
DocumentApp.TableCell.prototype.setTextAlignment = function(textAlignment) {};

/**
 * @param {DocumentApp.VerticalAlignment} alignment
 * @return {DocumentApp.TableCell}
 */
DocumentApp.TableCell.prototype.setVerticalAlignment = function(alignment) {};

/**
 * @param {number} width
 * @return {DocumentApp.TableCell}
 */
DocumentApp.TableCell.prototype.setWidth = function(width) {};

/** @constructor */
DocumentApp.TableOfContents = function() {};

/**
 * @return {DocumentApp.TableOfContents}
 */
DocumentApp.TableOfContents.prototype.clear = function() {};

/**
 * @return {DocumentApp.TableOfContents}
 */
DocumentApp.TableOfContents.prototype.copy = function() {};

/**
 * @return {DocumentApp.Text}
 */
DocumentApp.TableOfContents.prototype.editAsText = function() {};

/**
 * @param {DocumentApp.ElementType} elementType
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.TableOfContents.prototype.findElement = function(elementType, opt_from) {};

/**
 * @param {string} searchPattern
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.TableOfContents.prototype.findText = function(searchPattern, opt_from) {};

/**
 * @return {Object}
 */
DocumentApp.TableOfContents.prototype.getAttributes = function() {};

/**
 * @param {number} childIndex
 * @return {DocumentApp.Element}
 */
DocumentApp.TableOfContents.prototype.getChild = function(childIndex) {};

/**
 * @param {DocumentApp.Element} child
 * @return {number}
 */
DocumentApp.TableOfContents.prototype.getChildIndex = function(child) {};

/**
 * @return {string}
 */
DocumentApp.TableOfContents.prototype.getLinkUrl = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.TableOfContents.prototype.getNextSibling = function() {};

/**
 * @return {number}
 */
DocumentApp.TableOfContents.prototype.getNumChildren = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.TableOfContents.prototype.getParent = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.TableOfContents.prototype.getPreviousSibling = function() {};

/**
 * @return {string}
 */
DocumentApp.TableOfContents.prototype.getText = function() {};

/**
 * @return {DocumentApp.TextAlignment}
 */
DocumentApp.TableOfContents.prototype.getTextAlignment = function() {};

/**
 * @return {DocumentApp.ElementType}
 */
DocumentApp.TableOfContents.prototype.getType = function() {};

/**
 * @return {boolean}
 */
DocumentApp.TableOfContents.prototype.isAtDocumentEnd = function() {};

/**
 * @return {DocumentApp.TableOfContents}
 */
DocumentApp.TableOfContents.prototype.removeFromParent = function() {};

/**
 * @param {string} searchPattern
 * @param {string} replacement
 * @return {DocumentApp.Element}
 */
DocumentApp.TableOfContents.prototype.replaceText = function(searchPattern, replacement) {};

/**
 * @param {Object} attributes
 * @return {DocumentApp.TableOfContents}
 */
DocumentApp.TableOfContents.prototype.setAttributes = function(attributes) {};

/**
 * @param {string} url
 * @return {DocumentApp.TableOfContents}
 */
DocumentApp.TableOfContents.prototype.setLinkUrl = function(url) {};

/**
 * @param {DocumentApp.TextAlignment} textAlignment
 * @return {DocumentApp.TableOfContents}
 */
DocumentApp.TableOfContents.prototype.setTextAlignment = function(textAlignment) {};

/** @constructor */
DocumentApp.TableRow = function() {};

/**
 * @param {DocumentApp.TableCell|string|undefined} opt_tableCellOrTextContents
 * @return {DocumentApp.TableCell}
 */
DocumentApp.TableRow.prototype.appendTableCell = function(opt_tableCellOrTextContents) {};

/**
 * @return {DocumentApp.TableRow}
 */
DocumentApp.TableRow.prototype.clear = function() {};

/**
 * @return {DocumentApp.TableRow}
 */
DocumentApp.TableRow.prototype.copy = function() {};

/**
 * @return {DocumentApp.Text}
 */
DocumentApp.TableRow.prototype.editAsText = function() {};

/**
 * @param {DocumentApp.ElementType} elementType
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.TableRow.prototype.findElement = function(elementType, opt_from) {};

/**
 * @param {string} searchPattern
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.TableRow.prototype.findText = function(searchPattern, opt_from) {};

/**
 * @return {Object}
 */
DocumentApp.TableRow.prototype.getAttributes = function() {};

/**
 * @param {number} cellIndex
 * @return {DocumentApp.TableCell}
 */
DocumentApp.TableRow.prototype.getCell = function(cellIndex) {};

/**
 * @param {number} childIndex
 * @return {DocumentApp.Element}
 */
DocumentApp.TableRow.prototype.getChild = function(childIndex) {};

/**
 * @param {DocumentApp.Element} child
 * @return {number}
 */
DocumentApp.TableRow.prototype.getChildIndex = function(child) {};

/**
 * @return {string}
 */
DocumentApp.TableRow.prototype.getLinkUrl = function() {};

/**
 * @return {number}
 */
DocumentApp.TableRow.prototype.getMinimumHeight = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.TableRow.prototype.getNextSibling = function() {};

/**
 * @return {number}
 */
DocumentApp.TableRow.prototype.getNumCells = function() {};

/**
 * @return {number}
 */
DocumentApp.TableRow.prototype.getNumChildren = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.TableRow.prototype.getParent = function() {};

/**
 * @return {DocumentApp.Table}
 */
DocumentApp.TableRow.prototype.getParentTable = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.TableRow.prototype.getPreviousSibling = function() {};

/**
 * @return {string}
 */
DocumentApp.TableRow.prototype.getText = function() {};

/**
 * @return {DocumentApp.TextAlignment}
 */
DocumentApp.TableRow.prototype.getTextAlignment = function() {};

/**
 * @return {DocumentApp.ElementType}
 */
DocumentApp.TableRow.prototype.getType = function() {};

/**
 * @param {number} childIndex
 * @param {DocumentApp.TableCell|string|undefined} opt_tableCellOrTextContents
 * @return {DocumentApp.TableCell}
 */
DocumentApp.TableRow.prototype.insertTableCell = function(childIndex, opt_tableCellOrTextContents) {};

/**
 * @return {boolean}
 */
DocumentApp.TableRow.prototype.isAtDocumentEnd = function() {};

/**
 * @return {DocumentApp.TableRow}
 */
DocumentApp.TableRow.prototype.merge = function() {};

/**
 * @param {number} cellIndex
 * @return {DocumentApp.TableCell}
 */
DocumentApp.TableRow.prototype.removeCell = function(cellIndex) {};

/**
 * @param {DocumentApp.Element} child
 * @return {DocumentApp.TableRow}
 */
DocumentApp.TableRow.prototype.removeChild = function(child) {};

/**
 * @return {DocumentApp.TableRow}
 */
DocumentApp.TableRow.prototype.removeFromParent = function() {};

/**
 * @param {string} searchPattern
 * @param {string} replacement
 * @return {DocumentApp.Element}
 */
DocumentApp.TableRow.prototype.replaceText = function(searchPattern, replacement) {};

/**
 * @param {Object} attributes
 * @return {DocumentApp.TableRow}
 */
DocumentApp.TableRow.prototype.setAttributes = function(attributes) {};

/**
 * @param {string} url
 * @return {DocumentApp.TableRow}
 */
DocumentApp.TableRow.prototype.setLinkUrl = function(url) {};

/**
 * @param {number} minHeight
 * @return {DocumentApp.TableRow}
 */
DocumentApp.TableRow.prototype.setMinimumHeight = function(minHeight) {};

/**
 * @param {DocumentApp.TextAlignment} textAlignment
 * @return {DocumentApp.TableRow}
 */
DocumentApp.TableRow.prototype.setTextAlignment = function(textAlignment) {};

/** @constructor */
DocumentApp.Text = function() {};

/**
 * @param {string} text
 * @return {DocumentApp.Text}
 */
DocumentApp.Text.prototype.appendText = function(text) {};

/**
 * @return {DocumentApp.Text}
 */
DocumentApp.Text.prototype.copy = function() {};

/**
 * @param {number} startOffset
 * @param {number} endOffsetInclusive
 * @return {DocumentApp.Text}
 */
DocumentApp.Text.prototype.deleteText = function(startOffset, endOffsetInclusive) {};

/**
 * @return {DocumentApp.Text}
 */
DocumentApp.Text.prototype.editAsText = function() {};

/**
 * @param {string} searchPattern
 * @param {DocumentApp.RangeElement=} opt_from
 * @return {DocumentApp.RangeElement}
 */
DocumentApp.Text.prototype.findText = function(searchPattern, opt_from) {};

/**
 * @param {number=} opt_offset
 * @return {Object}
 */
DocumentApp.Text.prototype.getAttributes = function(opt_offset) {};

/**
 * @param {number=} opt_offset
 * @return {string}
 */
DocumentApp.Text.prototype.getBackgroundColor = function(opt_offset) {};

/**
 * @param {number=} opt_offset
 * @return {string}
 */
DocumentApp.Text.prototype.getFontFamily = function(opt_offset) {};

/**
 * @param {number=} opt_offset
 * @return {number}
 */
DocumentApp.Text.prototype.getFontSize = function(opt_offset) {};

/**
 * @param {number=} opt_offset
 * @return {string}
 */
DocumentApp.Text.prototype.getForegroundColor = function(opt_offset) {};

/**
 * @param {number=} opt_offset
 * @return {string}
 */
DocumentApp.Text.prototype.getLinkUrl = function(opt_offset) {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.Text.prototype.getNextSibling = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.Text.prototype.getParent = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.Text.prototype.getPreviousSibling = function() {};

/**
 * @return {string}
 */
DocumentApp.Text.prototype.getText = function() {};

/**
 * @param {number=} opt_offset
 * @return {DocumentApp.TextAlignment}
 */
DocumentApp.Text.prototype.getTextAlignment = function(opt_offset) {};

/**
 * @return {Array<number>}
 */
DocumentApp.Text.prototype.getTextAttributeIndices = function() {};

/**
 * @return {DocumentApp.ElementType}
 */
DocumentApp.Text.prototype.getType = function() {};

/**
 * @param {number} offset
 * @param {string} text
 * @return {DocumentApp.Text}
 */
DocumentApp.Text.prototype.insertText = function(offset, text) {};

/**
 * @return {boolean}
 */
DocumentApp.Text.prototype.isAtDocumentEnd = function() {};

/**
 * @param {number=} opt_offset
 * @return {boolean}
 */
DocumentApp.Text.prototype.isBold = function(opt_offset) {};

/**
 * @param {number=} opt_offset
 * @return {boolean}
 */
DocumentApp.Text.prototype.isItalic = function(opt_offset) {};

/**
 * @param {number=} opt_offset
 * @return {boolean}
 */
DocumentApp.Text.prototype.isStrikethrough = function(opt_offset) {};

/**
 * @param {number=} opt_offset
 * @return {boolean}
 */
DocumentApp.Text.prototype.isUnderline = function(opt_offset) {};

/**
 * @return {DocumentApp.Text}
 */
DocumentApp.Text.prototype.merge = function() {};

/**
 * @return {DocumentApp.Text}
 */
DocumentApp.Text.prototype.removeFromParent = function() {};

/**
 * @param {string} searchPattern
 * @param {string} replacement
 * @return {DocumentApp.Element}
 */
DocumentApp.Text.prototype.replaceText = function(searchPattern, replacement) {};

/**
 * @param {Object|number} attributesOrStartOffset
 * @param {number=} opt_endOffsetInclusive
 * @param {Object=} opt_attributes
 * @return {DocumentApp.Text}
 */
DocumentApp.Text.prototype.setAttributes = function(attributesOrStartOffset, opt_endOffsetInclusive, opt_attributes) {};

/**
 * @param {number|string} colorOrStartOffset
 * @param {number=} opt_endOffsetInclusive
 * @param {string=} opt_color
 * @return {DocumentApp.Text}
 */
DocumentApp.Text.prototype.setBackgroundColor = function(colorOrStartOffset, opt_endOffsetInclusive, opt_color) {};

/**
 * @param {boolean|number} boldOrStartOffset
 * @param {number=} opt_endOffsetInclusive
 * @param {boolean=} opt_bold
 * @return {DocumentApp.Text}
 */
DocumentApp.Text.prototype.setBold = function(boldOrStartOffset, opt_endOffsetInclusive, opt_bold) {};

/**
 * @param {number|string} fontFamilyNameOrStartOffset
 * @param {number=} opt_endOffsetInclusive
 * @param {string=} opt_fontFamilyName
 * @return {DocumentApp.Text}
 */
DocumentApp.Text.prototype.setFontFamily = function(fontFamilyNameOrStartOffset, opt_endOffsetInclusive, opt_fontFamilyName) {};

/**
 * @param {number} sizeOrStartOffset
 * @param {number=} opt_endOffsetInclusive
 * @param {number=} opt_size
 * @return {DocumentApp.Text}
 */
DocumentApp.Text.prototype.setFontSize = function(sizeOrStartOffset, opt_endOffsetInclusive, opt_size) {};

/**
 * @param {number|string} colorOrStartOffset
 * @param {number=} opt_endOffsetInclusive
 * @param {string=} opt_color
 * @return {DocumentApp.Text}
 */
DocumentApp.Text.prototype.setForegroundColor = function(colorOrStartOffset, opt_endOffsetInclusive, opt_color) {};

/**
 * @param {boolean|number} italicOrStartOffset
 * @param {number=} opt_endOffsetInclusive
 * @param {boolean=} opt_italic
 * @return {DocumentApp.Text}
 */
DocumentApp.Text.prototype.setItalic = function(italicOrStartOffset, opt_endOffsetInclusive, opt_italic) {};

/**
 * @param {number|string} startOffsetOrUrl
 * @param {number=} opt_endOffsetInclusive
 * @param {string=} opt_url
 * @return {DocumentApp.Text}
 */
DocumentApp.Text.prototype.setLinkUrl = function(startOffsetOrUrl, opt_endOffsetInclusive, opt_url) {};

/**
 * @param {boolean|number} startOffsetOrStrikethrough
 * @param {number=} opt_endOffsetInclusive
 * @param {boolean=} opt_strikethrough
 * @return {DocumentApp.Text}
 */
DocumentApp.Text.prototype.setStrikethrough = function(startOffsetOrStrikethrough, opt_endOffsetInclusive, opt_strikethrough) {};

/**
 * @param {string} text
 * @return {DocumentApp.Text}
 */
DocumentApp.Text.prototype.setText = function(text) {};

/**
 * @param {DocumentApp.TextAlignment|number} startOffsetOrTextAlignment
 * @param {number=} opt_endOffsetInclusive
 * @param {DocumentApp.TextAlignment=} opt_textAlignment
 * @return {DocumentApp.Text}
 */
DocumentApp.Text.prototype.setTextAlignment = function(startOffsetOrTextAlignment, opt_endOffsetInclusive, opt_textAlignment) {};

/**
 * @param {boolean|number} startOffsetOrUnderline
 * @param {number=} opt_endOffsetInclusive
 * @param {boolean=} opt_underline
 * @return {DocumentApp.Text}
 */
DocumentApp.Text.prototype.setUnderline = function(startOffsetOrUnderline, opt_endOffsetInclusive, opt_underline) {};

/** @enum {string} */
DocumentApp.TextAlignment = {
  NORMAL: '',
  SUBSCRIPT: '',
  SUPERSCRIPT: ''
};

/** @constructor */
DocumentApp.UnsupportedElement = function() {};

/**
 * @return {DocumentApp.UnsupportedElement}
 */
DocumentApp.UnsupportedElement.prototype.copy = function() {};

/**
 * @return {Object}
 */
DocumentApp.UnsupportedElement.prototype.getAttributes = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.UnsupportedElement.prototype.getNextSibling = function() {};

/**
 * @return {DocumentApp.ContainerElement}
 */
DocumentApp.UnsupportedElement.prototype.getParent = function() {};

/**
 * @return {DocumentApp.Element}
 */
DocumentApp.UnsupportedElement.prototype.getPreviousSibling = function() {};

/**
 * @return {DocumentApp.ElementType}
 */
DocumentApp.UnsupportedElement.prototype.getType = function() {};

/**
 * @return {boolean}
 */
DocumentApp.UnsupportedElement.prototype.isAtDocumentEnd = function() {};

/**
 * @return {DocumentApp.UnsupportedElement}
 */
DocumentApp.UnsupportedElement.prototype.merge = function() {};

/**
 * @return {DocumentApp.UnsupportedElement}
 */
DocumentApp.UnsupportedElement.prototype.removeFromParent = function() {};

/**
 * @param {Object} attributes
 * @return {DocumentApp.UnsupportedElement}
 */
DocumentApp.UnsupportedElement.prototype.setAttributes = function(attributes) {};

/** @enum {string} */
DocumentApp.VerticalAlignment = {
  BOTTOM: '',
  CENTER: '',
  TOP: ''
};