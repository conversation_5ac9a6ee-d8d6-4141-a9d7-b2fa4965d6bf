# Google Apps Script API Reference

## Overview

Apps Script services provide ways for your script to access data on Google and external systems. These services are built into the Apps Script environment and don't require imports or manual authorization controls.

## Types of Services

### Google Services
Services that let you access data from Google Workspace apps and other Google products:

- Drive
- Gmail 
- Sheets
- Calendar
- Docs
- Forms
- Slides
- Maps
- Translate
- And more...

### Utility Services
Services not connected to specific Google products that provide general functionality:

- Logging
- HTML creation
- Data compression
- And more...

## Advanced Services

Some Google services are offered as "advanced services" which:
- Provide thin wrappers around Google product APIs
- Include Google Workspace product APIs
- Require explicit enabling in the Apps Script project
- Need additional setup in Google Cloud Console

## Service Categories

### Google Workspace Services

#### Admin Console
- Directory API
- Enterprise License Manager API
- Groups Migration API
- Groups Settings API
- Reseller API
- Reports API

#### Calendar
##### CalendarApp
Main entry point for interacting with Google Calendar:
- `getCalendarById(id: string) → Calendar`
  - Gets calendar by ID
  - Parameters:
    - id: Calendar ID string
  - Returns: Calendar object or null if not found

- `getDefaultCalendar() → Calendar`
  - Gets user's default calendar
  - Returns: Default Calendar object

- `getAllCalendars() → Calendar[]`
  - Gets all calendars user has access to
  - Returns: Array of Calendar objects

- `createCalendar(name: string) → Calendar`
  - Creates new calendar
  - Parameters:
    - name: Calendar name
  - Returns: Newly created Calendar object

- `getEventsForDay(date: Date) → CalendarEvent[]`
  - Gets events for specific date
  - Parameters:
    - date: Date object
  - Returns: Array of CalendarEvent objects

- `createEvent(title: string, startTime: Date, endTime: Date) → CalendarEvent`
  - Creates calendar event
  - Parameters:
    - title: Event title
    - startTime: Start date/time
    - endTime: End date/time
  - Returns: Created CalendarEvent object

- `createEventFromDescription(description: string) → CalendarEvent`
  - Creates event from text description (e.g., "Meeting tomorrow at 3pm")
  - Parameters:
    - description: Natural language description
  - Returns: Created CalendarEvent object

- `createAllDayEvent(title: string, date: Date) → CalendarEvent`
  - Creates all-day event
  - Parameters:
    - title: Event title
    - date: Event date
  - Returns: Created CalendarEvent object

- `getEventsForCalendar(calendar: Calendar) → CalendarEvent[]`
  - Gets events for specific calendar
  - Parameters:
    - calendar: Calendar object
  - Returns: Array of CalendarEvent objects

##### Calendar Class
Represents a Google Calendar:
- `getId() → string`
  - Gets calendar ID
  - Returns: Unique calendar identifier

- `getName() → string`
  - Gets calendar name
  - Returns: Calendar display name

- `setName(name: string) → Calendar`
  - Sets calendar name
  - Parameters:
    - name: New calendar name
  - Returns: Calendar object for chaining

- `getDescription() → string`
  - Gets calendar description
  - Returns: Calendar description text

- `setDescription(description: string) → Calendar`
  - Sets calendar description
  - Parameters:
    - description: New description text
  - Returns: Calendar object for chaining

- `getColor() → string`
  - Gets calendar color
  - Returns: Color identifier string

- `setColor(color: string) → Calendar`
  - Sets calendar color
  - Parameters:
    - color: Color identifier from Color enum
  - Returns: Calendar object for chaining

- `isOwnedByMe() → boolean`
  - Checks if user owns calendar
  - Returns: true if user owns calendar, false otherwise

- `getEvents(startTime: Date, endTime: Date) → CalendarEvent[]`
  - Gets events in time range
  - Parameters:
    - startTime: Range start date/time
    - endTime: Range end date/time
  - Returns: Array of CalendarEvent objects

- `createEvent(title: string, startTime: Date, endTime: Date) → CalendarEvent`
  - Creates new event
  - Parameters:
    - title: Event title
    - startTime: Start date/time
    - endTime: End date/time
  - Returns: Created CalendarEvent object

- `deleteCalendar() → void`
  - Deletes calendar permanently
  - Returns: Nothing

##### CalendarEvent Class
Represents a calendar event:
- `getId() → string`
  - Gets event ID
  - Returns: Unique event identifier

- `getTitle() → string`
  - Gets event title
  - Returns: Event title text

- `setTitle(title: string) → CalendarEvent`
  - Sets event title
  - Parameters:
    - title: New event title
  - Returns: CalendarEvent object for chaining

- `getDescription() → string`
  - Gets event description
  - Returns: Event description text

- `setDescription(description: string) → CalendarEvent`
  - Sets event description
  - Parameters:
    - description: New description text
  - Returns: CalendarEvent object for chaining

- `getStartTime() → Date`
  - Gets start time
  - Returns: Event start date/time

- `setStartTime(date: Date) → CalendarEvent`
  - Sets start time
  - Parameters:
    - date: New start date/time
  - Returns: CalendarEvent object for chaining

- `getEndTime() → Date`
  - Gets end time
  - Returns: Event end date/time

- `setEndTime(date: Date) → CalendarEvent`
  - Sets end time
  - Parameters:
    - date: New end date/time
  - Returns: CalendarEvent object for chaining

- `getLocation() → string`
  - Gets event location
  - Returns: Location text or empty string if none

- `setLocation(location: string) → CalendarEvent`
  - Sets event location
  - Parameters:
    - location: Location text
  - Returns: CalendarEvent object for chaining

- `getGuests() → EventGuest[]`
  - Gets list of guests
  - Returns: Array of EventGuest objects

- `addGuest(email: string) → CalendarEvent`
  - Adds guest by email
  - Parameters:
    - email: Guest's email address
  - Returns: CalendarEvent object for chaining

- `removeGuest(email: string) → CalendarEvent`
  - Removes guest by email
  - Parameters:
    - email: Guest's email address
  - Returns: CalendarEvent object for chaining

- `deleteEvent() → void`
  - Deletes event permanently
  - Returns: Nothing

##### EventGuest Class
Represents an event guest/attendee:
- `getEmail() → string`
  - Gets guest email address
  - Returns: Guest's email address

- `getName() → string`
  - Gets guest display name
  - Returns: Guest's name or empty string if not available

- `getStatus() → string`
  - Gets response status
  - Returns: "YES", "NO", "MAYBE", or "INVITED"

- `getGuestStatus() → GuestStatus`
  - Gets detailed guest status
  - Returns: GuestStatus enum value

- `isOptional() → boolean`
  - Checks if guest is optional
  - Returns: true if optional attendee, false if required

- `isOrganizer() → boolean`
  - Checks if guest is event organizer
  - Returns: true if organizer, false otherwise

##### Enums
###### Color
Calendar color options:
- `BLUE`
- `GREEN` 
- `RED`
- `ORANGE`
- etc.

###### EventColor  
Event-specific colors:
- `PALE_BLUE`
- `PALE_GREEN`
- `MAUVE`
- `PALE_RED`
- etc.

###### EventTransparency
Event availability settings:
- `OPAQUE`: Shows as busy
- `TRANSPARENT`: Shows as free

###### EventType
Types of calendar events:
- `DEFAULT`: Regular event
- `ALL_DAY`: All-day event
- `RECURRING`: Recurring event

###### GuestStatus
Possible guest response statuses:
- `YES`: Attending
- `NO`: Not attending
- `MAYBE`: Maybe attending
- `INVITED`: Not responded yet

###### Visibility  
Calendar visibility settings:
- `DEFAULT`: Default visibility
- `PUBLIC`: Visible to public
- `PRIVATE`: Private to owner

#### Chat
- Chat API

#### Docs
##### DocumentApp
Main entry point for Google Docs operations:
- `create(name: string) → Document`
  - Creates new document
  - Parameters:
    - name: Document name
  - Returns: New Document object

- `openById(id: string) → Document`
  - Opens document by ID
  - Parameters:
    - id: Document ID
  - Returns: Document object or null if not found

- `openByUrl(url: string) → Document`
  - Opens document by URL
  - Parameters:
    - url: Document URL
  - Returns: Document object or null if not found

- `getActiveDocument() → Document`
  - Gets active document
  - Returns: Currently active Document object

- `getUi() → Ui`
  - Gets document UI
  - Returns: UI object for displaying dialogs/menus

- `getBody() → Body`
  - Gets document body
  - Returns: Body object containing main content

- `getCursor() → Position`
  - Gets cursor position
  - Returns: Current cursor Position object or null if none

- `getSelection() → Range`
  - Gets selected text
  - Returns: Selected Range object or null if none

- `saveAndClose() → void`
  - Saves changes and closes document
  - Returns: Nothing

##### Document Class
Represents a Google Doc:
- `getId() → string`
  - Gets document ID
  - Returns: Unique document identifier

- `getName() → string`
  - Gets document name
  - Returns: Document display name

- `setName(name: string) → Document`
  - Sets document name
  - Parameters:
    - name: New document name
  - Returns: Document object for chaining

- `getUrl() → string`
  - Gets document URL
  - Returns: Document's web URL

- `getBody() → Body`
  - Gets document body
  - Returns: Body object containing main content

- `getHeader() → HeaderSection`
  - Gets header section
  - Returns: HeaderSection object or null if none

- `getFooter() → FooterSection`
  - Gets footer section
  - Returns: FooterSection object or null if none

- `getSelection() → Range`
  - Gets current selection
  - Returns: Selected Range object or null if none

- `setCursor(position: Position) → Document`
  - Sets cursor position
  - Parameters:
    - position: Position object specifying location
  - Returns: Document object for chaining

- `saveAndClose() → void`
  - Saves document and closes
  - Returns: Nothing

##### Body Class
Represents document body:
- `getText() → string`
  - Gets all text content
  - Returns: Document's text content

- `setText(text: string) → Body`
  - Sets all text content
  - Parameters:
    - text: New text content
  - Returns: Body object for chaining

- `clear() → Body`
  - Clears all content
  - Returns: Body object for chaining

- `appendParagraph(text: string) → Paragraph`
  - Adds paragraph at end
  - Parameters:
    - text: Paragraph text content
  - Returns: New Paragraph object

- `appendTable() → Table`
  - Adds empty table at end
  - Returns: New Table object

- `appendImage(image: BlobSource) → InlineImage`
  - Adds image at end
  - Parameters:
    - image: Image data as blob
  - Returns: New InlineImage object

- `appendHorizontalRule() → HorizontalRule`
  - Adds horizontal line at end
  - Returns: New HorizontalRule object

- `appendPageBreak() → PageBreak`
  - Adds page break at end
  - Returns: New PageBreak object

- `getNumChildren() → Integer`
  - Gets number of child elements
  - Returns: Count of child elements

- `getChild(index: Integer) → Element`
  - Gets child element at index
  - Parameters:
    - index: Zero-based index
  - Returns: Element object at index

- `removeChild(child: Element) → Body`
  - Removes specified child element
  - Parameters:
    - child: Element to remove
  - Returns: Body object for chaining

##### Paragraph Class
Represents a paragraph:
- `getText() → string`
  - Gets paragraph text content
  - Returns: Paragraph's text content

- `setText(text: string) → Paragraph`
  - Sets paragraph text content
  - Parameters:
    - text: New text content
  - Returns: Paragraph object for chaining

- `appendText(text: string) → Paragraph`
  - Adds text to end of paragraph
  - Parameters:
    - text: Text to append
  - Returns: Paragraph object for chaining

- `setHeading(heading: ParagraphHeading) → Paragraph`
  - Sets heading style
  - Parameters:
    - heading: ParagraphHeading enum value
  - Returns: Paragraph object for chaining

- `setAlignment(alignment: HorizontalAlignment) → Paragraph`
  - Sets text alignment
  - Parameters:
    - alignment: HorizontalAlignment enum value
  - Returns: Paragraph object for chaining

- `setIndentStart(indent: number) → Paragraph`
  - Sets left indent in points
  - Parameters:
    - indent: Indent size in points
  - Returns: Paragraph object for chaining

- `setIndentEnd(indent: number) → Paragraph`
  - Sets right indent in points
  - Parameters:
    - indent: Indent size in points
  - Returns: Paragraph object for chaining

- `setLineSpacing(spacing: number) → Paragraph`
  - Sets line spacing
  - Parameters:
    - spacing: Line spacing value
  - Returns: Paragraph object for chaining

- `setSpacingAfter(spacing: number) → Paragraph`
  - Sets spacing after paragraph
  - Parameters:
    - spacing: Space in points
  - Returns: Paragraph object for chaining

- `setSpacingBefore(spacing: number) → Paragraph`
  - Sets spacing before paragraph
  - Parameters:
    - spacing: Space in points
  - Returns: Paragraph object for chaining

##### Table Class
Represents a table:
- `getNumRows() → Integer`
  - Gets number of rows
  - Returns: Row count

- `getNumCols() → Integer`
  - Gets number of columns
  - Returns: Column count

- `getRow(rowIndex: Integer) → TableRow`
  - Gets row at index
  - Parameters:
    - rowIndex: Zero-based row index
  - Returns: TableRow object

- `appendRow() → TableRow`
  - Adds new row at end
  - Returns: New TableRow object

- `insertRow(rowIndex: Integer) → TableRow`
  - Inserts row at index
  - Parameters:
    - rowIndex: Zero-based insertion index
  - Returns: New TableRow object

- `removeRow(rowIndex: Integer) → Table`
  - Removes row at index
  - Parameters:
    - rowIndex: Zero-based row index
  - Returns: Table object for chaining

- `getCell(row: Integer, col: Integer) → TableCell`
  - Gets cell at coordinates
  - Parameters:
    - row: Zero-based row index
    - col: Zero-based column index
  - Returns: TableCell object

- `setBorderColor(color: string) → Table`
  - Sets table border color
  - Parameters:
    - color: CSS color value
  - Returns: Table object for chaining

- `setBorderWidth(width: number) → Table`
  - Sets table border width
  - Parameters:
    - width: Border width in points
  - Returns: Table object for chaining

##### ElementType Enum
Element types:
- `PARAGRAPH`: Text paragraph
- `TABLE`: Table element
- `LIST_ITEM`: List item
- `INLINE_IMAGE`: Inline image
- `PAGE_BREAK`: Page break
- `HORIZONTAL_RULE`: Horizontal line
- `FOOTNOTE`: Footnote
- `HEADER_SECTION`: Header
- `FOOTER_SECTION`: Footer

##### FontFamily Enum
Font families:
- `ARIAL`: Arial font
- `CALIBRI`: Calibri font
- `CAMBRIA`: Cambria font
- `COMIC_SANS_MS`: Comic Sans
- `COURIER_NEW`: Courier New
- `GEORGIA`: Georgia font
- `HELVETICA`: Helvetica font
- `TIMES_NEW_ROMAN`: Times New Roman
- `TREBUCHET_MS`: Trebuchet MS
- `VERDANA`: Verdana font

#### Drive
##### DriveApp
Main entry point for Google Drive operations:
- `createFile(blob: BlobSource) → File`
  - Creates file from blob
  - Parameters:
    - blob: File content as blob
  - Returns: New File object

- `createFolder(name: string) → Folder`
  - Creates new folder
  - Parameters:
    - name: Folder name
  - Returns: New Folder object

- `getFileById(id: string) → File`
  - Gets file by ID
  - Parameters:
    - id: File ID
  - Returns: File object or null if not found

- `getFolderById(id: string) → Folder`
  - Gets folder by ID
  - Parameters:
    - id: Folder ID
  - Returns: Folder object or null if not found

- `getFiles() → FileIterator`
  - Gets all accessible files
  - Returns: Iterator over File objects

- `getFolders() → FolderIterator`
  - Gets all accessible folders
  - Returns: Iterator over Folder objects

- `getRootFolder() → Folder`
  - Gets root Drive folder
  - Returns: Root Folder object

- `getTrashFiles() → FileIterator`
  - Gets files in trash
  - Returns: Iterator over trashed File objects

- `searchFiles(query: string) → FileIterator`
  - Searches for files
  - Parameters:
    - query: Search query string
  - Returns: Iterator over matching File objects

- `searchFolders(query: string) → FolderIterator`
  - Searches for folders
  - Parameters:
    - query: Search query string
  - Returns: Iterator over matching Folder objects

- `getStorageLimit() → Integer`
  - Gets storage quota
  - Returns: Storage limit in bytes

- `getStorageUsed() → Integer`
  - Gets storage used
  - Returns: Storage used in bytes

##### File Class
Represents a Drive file:
- `getId() → string`
  - Gets file ID
  - Returns: Unique file identifier

- `getName() → string`
  - Gets file name
  - Returns: File display name

- `setName(name: string) → File`
  - Sets file name
  - Parameters:
    - name: New file name
  - Returns: File object for chaining

- `getDescription() → string`
  - Gets file description
  - Returns: File description text

- `setDescription(description: string) → File`
  - Sets file description
  - Parameters:
    - description: New description text
  - Returns: File object for chaining

- `getDateCreated() → Date`
  - Gets creation date
  - Returns: File creation timestamp

- `getLastUpdated() → Date`
  - Gets last modified date
  - Returns: Last modification timestamp

- `getSize() → Integer`
  - Gets file size
  - Returns: Size in bytes

- `getMimeType() → string`
  - Gets MIME type
  - Returns: File's MIME type

- `getUrl() → string`
  - Gets sharing URL
  - Returns: Web view URL

- `getDownloadUrl() → string`
  - Gets download URL
  - Returns: Direct download URL

- `getAs(contentType: string) → Blob`
  - Gets file as different type
  - Parameters:
    - contentType: Target MIME type
  - Returns: File content as Blob

- `getBlob() → Blob`
  - Gets file content as blob
  - Returns: File content Blob

- `getParents() → FolderIterator`
  - Gets parent folders
  - Returns: Iterator over parent Folder objects

- `makeCopy() → File`
  - Creates file copy
  - Returns: New File object

- `moveTo(folder: Folder) → File`
  - Moves to folder
  - Parameters:
    - folder: Destination Folder object
  - Returns: File object for chaining

- `removeFromParents() → File`
  - Removes from current folders
  - Returns: File object for chaining

- `setContent(content: string) → File`
  - Sets file content
  - Parameters:
    - content: New text content
  - Returns: File object for chaining

- `setSharing(access: Access, permission: Permission) → File`
  - Sets sharing settings
  - Parameters:
    - access: Access level from Access enum
    - permission: Permission level from Permission enum
  - Returns: File object for chaining

- `trash() → File`
  - Moves to trash
  - Returns: File object for chaining

- `delete() → void`
  - Permanently deletes file
  - Returns: Nothing

##### Folder Class
Represents a Drive folder:
- `getId() → string`
  - Gets folder ID
  - Returns: Unique folder identifier

- `getName() → string`
  - Gets folder name
  - Returns: Folder display name

- `setName(name: string) → Folder`
  - Sets folder name
  - Parameters:
    - name: New folder name
  - Returns: Folder object for chaining

- `getDescription() → string`
  - Gets folder description
  - Returns: Folder description text

- `setDescription(description: string) → Folder`
  - Sets folder description
  - Parameters:
    - description: New description text
  - Returns: Folder object for chaining

- `getDateCreated() → Date`
  - Gets creation date
  - Returns: Folder creation timestamp

- `getLastUpdated() → Date`
  - Gets last modified date
  - Returns: Last modification timestamp

- `getSize() → Integer`
  - Gets folder size
  - Returns: Total size in bytes

- `getUrl() → string`
  - Gets sharing URL
  - Returns: Web view URL

- `getFiles() → FileIterator`
  - Gets contained files
  - Returns: Iterator over contained File objects

- `getFolders() → FolderIterator`
  - Gets subfolders
  - Returns: Iterator over subfolder Folder objects

- `getParents() → FolderIterator`
  - Gets parent folders
  - Returns: Iterator over parent Folder objects

- `createFile(blob: BlobSource) → File`
  - Creates new file
  - Parameters:
    - blob: File content as blob
  - Returns: New File object

- `createFolder(name: string) → Folder`
  - Creates subfolder
  - Parameters:
    - name: Folder name
  - Returns: New Folder object

- `moveTo(folder: Folder) → Folder`
  - Moves to another folder
  - Parameters:
    - folder: Destination Folder object
  - Returns: Folder object for chaining

- `removeFromParents() → Folder`
  - Removes from current location
  - Returns: Folder object for chaining

- `setSharing(access: Access, permission: Permission) → Folder`
  - Sets sharing settings
  - Parameters:
    - access: Access level from Access enum
    - permission: Permission level from Permission enum
  - Returns: Folder object for chaining

- `delete() → void`
  - Deletes folder and contents
  - Returns: Nothing

##### Access Enum
File/folder access levels:
- `ANYONE`: Public access
- `ANYONE_WITH_LINK`: Access with link
- `DOMAIN`: Organization access
- `DOMAIN_WITH_LINK`: Organization with link
- `PRIVATE`: Private access

##### Permission Enum
Access permission levels:
- `VIEW`: Read-only access
- `EDIT`: Can edit
- `COMMENT`: Can comment
- `OWNER`: Full ownership

#### Forms
##### FormApp
Main entry point for Google Forms operations:
- `create(title: string) → Form`
  - Creates new form
  - Parameters:
    - title: Form title
  - Returns: New Form object

- `openById(id: string) → Form`
  - Opens form by ID
  - Parameters:
    - id: Form ID
  - Returns: Form object or null if not found

- `openByUrl(url: string) → Form`
  - Opens form by URL
  - Parameters:
    - url: Form URL
  - Returns: Form object or null if not found

- `getActiveForm() → Form`
  - Gets active form
  - Returns: Currently active Form object

- `getUi() → Ui`
  - Gets form UI
  - Returns: UI object for displaying dialogs/menus

- `getResponses() → FormResponse[]`
  - Gets all responses
  - Returns: Array of FormResponse objects

- `getResponse(responseId: string) → FormResponse`
  - Gets specific response
  - Parameters:
    - responseId: Response ID
  - Returns: FormResponse object or null if not found

##### Form Class
Represents a Google Form:
- `getId() → string`
  - Gets form ID
  - Returns: Unique form identifier

- `getTitle() → string`
  - Gets form title
  - Returns: Form title text

- `setTitle(title: string) → Form`
  - Sets form title
  - Parameters:
    - title: New form title
  - Returns: Form object for chaining

- `getDescription() → string`
  - Gets form description
  - Returns: Form description text

- `setDescription(description: string) → Form`
  - Sets form description
  - Parameters:
    - description: New description text
  - Returns: Form object for chaining

- `getEditUrl() → string`
  - Gets edit URL
  - Returns: Form editor URL

- `getPublishedUrl() → string`
  - Gets public URL
  - Returns: Public form URL

- `getResponses() → FormResponse[]`
  - Gets all responses
  - Returns: Array of FormResponse objects

- `getItems() → Item[]`
  - Gets all items
  - Returns: Array of form items (questions)

- `addItem() → Item`
  - Adds new item
  - Returns: New Item object

- `deleteItem(index: Integer) → Form`
  - Deletes item
  - Parameters:
    - index: Zero-based item index
  - Returns: Form object for chaining

- `moveItem(index: Integer, newIndex: Integer) → Form`
  - Reorders items
  - Parameters:
    - index: Current zero-based index
    - newIndex: New zero-based index
  - Returns: Form object for chaining

- `setRequireLogin(required: boolean) → Form`
  - Sets login requirement
  - Parameters:
    - required: Whether to require login
  - Returns: Form object for chaining

- `setCollectEmail(collect: boolean) → Form`
  - Sets email collection
  - Parameters:
    - collect: Whether to collect email
  - Returns: Form object for chaining

- `setLimitOneResponsePerUser(limit: boolean) → Form`
  - Sets response limit
  - Parameters:
    - limit: Whether to limit to one response
  - Returns: Form object for chaining

- `setShowLinkToRespondAgain(show: boolean) → Form`
  - Shows/hides retry link
  - Parameters:
    - show: Whether to show link
  - Returns: Form object for chaining

- `setAcceptingResponses(accepting: boolean) → Form`
  - Opens/closes form
  - Parameters:
    - accepting: Whether to accept responses
  - Returns: Form object for chaining

- `setPublishingSummary(publish: boolean) → Form`
  - Shows/hides summary
  - Parameters:
    - publish: Whether to publish summary
  - Returns: Form object for chaining

- `setAllowResponseEdits(allow: boolean) → Form`
  - Allows/disallows edits
  - Parameters:
    - allow: Whether to allow edits
  - Returns: Form object for chaining

- `setProgressBar(enabled: boolean) → Form`
  - Shows/hides progress bar
  - Parameters:
    - enabled: Whether to show progress
  - Returns: Form object for chaining

- `setShuffleQuestions(shuffle: boolean) → Form`
  - Enables/disables shuffle
  - Parameters:
    - shuffle: Whether to shuffle questions
  - Returns: Form object for chaining

##### FormResponse Class
Represents a form response:
- `getId() → string`
  - Gets response ID
  - Returns: Unique response identifier

- `getEditResponseUrl() → string`
  - Gets edit URL
  - Returns: Response edit URL

- `getRespondentEmail() → string`
  - Gets respondent email
  - Returns: Respondent's email address

- `getTimestamp() → Date`
  - Gets submission time
  - Returns: Response submission timestamp

- `getItemResponses() → ItemResponse[]`
  - Gets all item responses
  - Returns: Array of ItemResponse objects

- `getResponseForItem(item: Item) → ItemResponse`
  - Gets specific item response
  - Parameters:
    - item: Form item to get response for
  - Returns: ItemResponse object or null if not found

- `withItemResponse(response: ItemResponse) → FormResponse`
  - Adds item response
  - Parameters:
    - response: ItemResponse object to add
  - Returns: FormResponse object for chaining

- `submit() → FormResponse`
  - Submits response
  - Returns: FormResponse object for chaining

##### Item Classes
###### TextItem
Text input field:
- `setTitle(title: string) → TextItem`
  - Sets question title
  - Parameters:
    - title: Question title text
  - Returns: TextItem object for chaining

- `setHelpText(text: string) → TextItem`
  - Sets help text
  - Parameters:
    - text: Help text content
  - Returns: TextItem object for chaining

- `setRequired(required: boolean) → TextItem`
  - Makes required/optional
  - Parameters:
    - required: Whether field is required
  - Returns: TextItem object for chaining

- `setValidation(validation: TextValidation) → TextItem`
  - Sets validation rules
  - Parameters:
    - validation: TextValidation object
  - Returns: TextItem object for chaining

###### MultipleChoiceItem
Multiple choice question:
- `setTitle(title: string) → MultipleChoiceItem`
  - Sets question title
  - Parameters:
    - title: Question title text
  - Returns: MultipleChoiceItem object for chaining

- `setChoices(choices: string[]) → MultipleChoiceItem`
  - Sets choice options
  - Parameters:
    - choices: Array of choice texts
  - Returns: MultipleChoiceItem object for chaining

- `showOtherOption(show: boolean) → MultipleChoiceItem`
  - Shows/hides "Other" option
  - Parameters:
    - show: Whether to show "Other"
  - Returns: MultipleChoiceItem object for chaining

- `setRequired(required: boolean) → MultipleChoiceItem`
  - Makes required/optional
  - Parameters:
    - required: Whether field is required
  - Returns: MultipleChoiceItem object for chaining

###### CheckboxItem
Checkbox question:
- `setTitle(title: string) → CheckboxItem`
  - Sets question title
  - Parameters:
    - title: Question title text
  - Returns: CheckboxItem object for chaining

- `setChoices(choices: string[]) → CheckboxItem`
  - Sets choice options
  - Parameters:
    - choices: Array of choice texts
  - Returns: CheckboxItem object for chaining

- `showOtherOption(show: boolean) → CheckboxItem`
  - Shows/hides "Other" option
  - Parameters:
    - show: Whether to show "Other"
  - Returns: CheckboxItem object for chaining

- `setRequired(required: boolean) → CheckboxItem`
  - Makes required/optional
  - Parameters:
    - required: Whether field is required
  - Returns: CheckboxItem object for chaining

###### ListItem
Dropdown list question:
- `setTitle(title: string) → ListItem`
  - Sets question title
  - Parameters:
    - title: Question title text
  - Returns: ListItem object for chaining

- `setChoices(choices: string[]) → ListItem`
  - Sets list options
  - Parameters:
    - choices: Array of choice texts
  - Returns: ListItem object for chaining

- `setRequired(required: boolean) → ListItem`
  - Makes required/optional
  - Parameters:
    - required: Whether field is required
  - Returns: ListItem object for chaining

###### GridItem
Grid of questions:
- `setTitle(title: string) → GridItem`
  - Sets grid title
  - Parameters:
    - title: Question title text
  - Returns: GridItem object for chaining

- `setRows(rows: string[]) → GridItem`
  - Sets row headers
  - Parameters:
    - rows: Array of row header texts
  - Returns: GridItem object for chaining

- `setColumns(columns: string[]) → GridItem`
  - Sets column options
  - Parameters:
    - columns: Array of column option texts
  - Returns: GridItem object for chaining

- `setRequired(required: boolean) → GridItem`
  - Makes required/optional
  - Parameters:
    - required: Whether field is required
  - Returns: GridItem object for chaining

##### Enums
###### ItemType
Question types:
- `TEXT`: Text input
- `PARAGRAPH_TEXT`: Long text
- `MULTIPLE_CHOICE`: Multiple choice
- `CHECKBOX`: Checkboxes
- `LIST`: Dropdown list
- `GRID`: Question grid
- `DATE`: Date input
- `TIME`: Time input
- `SCALE`: Linear scale
- `IMAGE`: Image upload
- `FILE_UPLOAD`: File upload
- `PAGE_BREAK`: Section break
- `SECTION_HEADER`: Section header

###### PageNavigationType
Form navigation types:
- `CONTINUE`: Continue button
- `SUBMIT`: Submit button
- `GO_TO_PAGE`: Page navigation
- `RESTART`: Restart form

#### Gmail
##### GmailApp
Main entry point for Gmail operations:
- `getInboxThreads() → GmailThread[]`
  - Gets threads in inbox
  - Returns: Array of inbox GmailThread objects

- `getInboxUnreadCount() → Integer`
  - Gets count of unread inbox messages
  - Returns: Number of unread messages

- `search(query: string) → GmailThread[]`
  - Searches for threads matching query
  - Parameters:
    - query: Gmail search query string
  - Returns: Array of matching GmailThread objects

- `sendEmail(recipient: string, subject: string, body: string) → void`
  - Sends email
  - Parameters:
    - recipient: Recipient email address
    - subject: Email subject line
    - body: Email body content
  - Returns: Nothing

- `createDraft(recipient: string, subject: string, body: string) → GmailDraft`
  - Creates draft email
  - Parameters:
    - recipient: Recipient email address
    - subject: Email subject line
    - body: Email body content
  - Returns: New GmailDraft object

- `getAliases() → string[]`
  - Gets user's email aliases
  - Returns: Array of email alias addresses

- `getDraftMessages() → GmailMessage[]`
  - Gets all draft messages
  - Returns: Array of draft GmailMessage objects

- `getSpamThreads() → GmailThread[]`
  - Gets threads in spam
  - Returns: Array of spam GmailThread objects

- `getStarredThreads() → GmailThread[]`
  - Gets starred threads
  - Returns: Array of starred GmailThread objects

- `getTrashThreads() → GmailThread[]`
  - Gets threads in trash
  - Returns: Array of trashed GmailThread objects

- `markMessageRead(message: GmailMessage) → GmailMessage`
  - Marks message as read
  - Parameters:
    - message: GmailMessage to mark
  - Returns: GmailMessage object for chaining

- `markMessageUnread(message: GmailMessage) → GmailMessage`
  - Marks message as unread
  - Parameters:
    - message: GmailMessage to mark
  - Returns: GmailMessage object for chaining

- `markThreadRead(thread: GmailThread) → GmailThread`
  - Marks thread as read
  - Parameters:
    - thread: GmailThread to mark
  - Returns: GmailThread object for chaining

- `markThreadUnread(thread: GmailThread) → GmailThread`
  - Marks thread as unread
  - Parameters:
    - thread: GmailThread to mark
  - Returns: GmailThread object for chaining

##### GmailMessage Class
Represents an email message:
- `getId() → string`
  - Gets message ID
  - Returns: Unique message identifier

- `getSubject() → string`
  - Gets message subject
  - Returns: Email subject line

- `getBody() → string`
  - Gets message body
  - Returns: Email body content

- `getFrom() → string`
  - Gets sender's email
  - Returns: Sender's email address

- `getTo() → string[]`
  - Gets recipient emails
  - Returns: Array of recipient email addresses

- `getCc() → string[]`
  - Gets CC recipients
  - Returns: Array of CC recipient email addresses

- `getBcc() → string[]`
  - Gets BCC recipients
  - Returns: Array of BCC recipient email addresses

- `getDate() → Date`
  - Gets message date
  - Returns: Message timestamp

- `getAttachments() → GmailAttachment[]`
  - Gets message attachments
  - Returns: Array of GmailAttachment objects

- `forward(recipient: string) → GmailMessage`
  - Forwards message
  - Parameters:
    - recipient: Forward recipient email address
  - Returns: GmailMessage object for chaining

- `reply(body: string) → GmailMessage`
  - Replies to message
  - Parameters:
    - body: Reply message content
  - Returns: GmailMessage object for chaining

- `replyAll(body: string) → GmailMessage`
  - Replies to all recipients
  - Parameters:
    - body: Reply message content
  - Returns: GmailMessage object for chaining

- `star() → GmailMessage`
  - Stars the message
  - Returns: GmailMessage object for chaining

- `unstar() → GmailMessage`
  - Removes star
  - Returns: GmailMessage object for chaining

- `moveToTrash() → GmailMessage`
  - Moves to trash
  - Returns: GmailMessage object for chaining

- `markRead() → GmailMessage`
  - Marks as read
  - Returns: GmailMessage object for chaining

- `markUnread() → GmailMessage`
  - Marks as unread
  - Returns: GmailMessage object for chaining

##### GmailThread Class
Represents a conversation thread:
- `getId() → string`
  - Gets thread ID
  - Returns: Unique thread identifier

- `getFirstMessageSubject() → string`
  - Gets first message subject
  - Returns: Subject line of first message

- `getLabels() → GmailLabel[]`
  - Gets thread labels
  - Returns: Array of GmailLabel objects

- `getLastMessageDate() → Date`
  - Gets last message date
  - Returns: Timestamp of most recent message

- `getMessageCount() → Integer`
  - Gets number of messages
  - Returns: Count of messages in thread

- `getMessages() → GmailMessage[]`
  - Gets all messages
  - Returns: Array of GmailMessage objects

- `addLabel(label: GmailLabel) → GmailThread`
  - Adds label to thread
  - Parameters:
    - label: GmailLabel to add
  - Returns: GmailThread object for chaining

- `removeLabel(label: GmailLabel) → GmailThread`
  - Removes label
  - Parameters:
    - label: GmailLabel to remove
  - Returns: GmailThread object for chaining

- `moveToArchive() → GmailThread`
  - Archives thread
  - Returns: GmailThread object for chaining

- `moveToInbox() → GmailThread`
  - Moves to inbox
  - Returns: GmailThread object for chaining

- `moveToSpam() → GmailThread`
  - Marks as spam
  - Returns: GmailThread object for chaining

- `moveToTrash() → GmailThread`
  - Moves to trash
  - Returns: GmailThread object for chaining

- `markRead() → GmailThread`
  - Marks thread as read
  - Returns: GmailThread object for chaining

- `markUnread() → GmailThread`
  - Marks thread as unread
  - Returns: GmailThread object for chaining

##### GmailLabel Class
Represents a Gmail label:
- `getName() → string`
  - Gets label name
  - Returns: Label display name

- `getThreads() → GmailThread[]`
  - Gets threads with label
  - Returns: Array of GmailThread objects

- `addToThread(thread: GmailThread) → GmailLabel`
  - Adds label to thread
  - Parameters:
    - thread: GmailThread to label
  - Returns: GmailLabel object for chaining

- `removeFromThread(thread: GmailThread) → GmailLabel`
  - Removes label from thread
  - Parameters:
    - thread: GmailThread to unlabel
  - Returns: GmailLabel object for chaining

- `deleteLabel() → void`
  - Deletes the label
  - Returns: Nothing

##### GmailAttachment Class
Represents an email attachment:
- `getName() → string`
  - Gets attachment name
  - Returns: Attachment filename

- `getSize() → Integer`
  - Gets file size
  - Returns: Size in bytes

- `getContentType() → string`
  - Gets MIME type
  - Returns: Attachment's MIME type

- `copyBlob() → Blob`
  - Gets attachment as blob
  - Returns: Attachment content as Blob

- `downloadAllAs(zip: boolean) → Blob`
  - Downloads all attachments
  - Parameters:
    - zip: Whether to compress as ZIP
  - Returns: Attachments as single Blob

#### Sheets
##### SpreadsheetApp
Main entry point for Google Sheets operations:
- `create(name: string) → Spreadsheet`
  - Creates new spreadsheet
  - Parameters:
    - name: Spreadsheet name
  - Returns: New Spreadsheet object

- `open(file: File) → Spreadsheet`
  - Opens spreadsheet from file
  - Parameters:
    - file: Drive File object
  - Returns: Spreadsheet object or null if not found

- `openById(id: string) → Spreadsheet`
  - Opens spreadsheet by ID
  - Parameters:
    - id: Spreadsheet ID
  - Returns: Spreadsheet object or null if not found

- `openByUrl(url: string) → Spreadsheet`
  - Opens spreadsheet by URL
  - Parameters:
    - url: Spreadsheet URL
  - Returns: Spreadsheet object or null if not found

- `getActive() → Spreadsheet`
  - Gets active spreadsheet
  - Returns: Currently active Spreadsheet object

- `getActiveSheet() → Sheet`
  - Gets active sheet
  - Returns: Currently active Sheet object

- `getActiveRange() → Range`
  - Gets selected range
  - Returns: Currently selected Range object

- `getActiveSpreadsheet() → Spreadsheet`
  - Gets active spreadsheet
  - Returns: Currently active Spreadsheet object

- `setActiveSheet(sheet: Sheet) → Sheet`
  - Sets active sheet
  - Parameters:
    - sheet: Sheet to activate
  - Returns: Sheet object for chaining

- `setActiveRange(range: Range) → Range`
  - Sets selected range
  - Parameters:
    - range: Range to select
  - Returns: Range object for chaining

- `flush() → void`
  - Applies pending changes
  - Returns: Nothing

##### Spreadsheet Class
Represents a spreadsheet:
- `getId() → string`
  - Gets spreadsheet ID
  - Returns: Unique spreadsheet identifier

- `getName() → string`
  - Gets spreadsheet name
  - Returns: Spreadsheet display name

- `setName(name: string) → Spreadsheet`
  - Sets spreadsheet name
  - Parameters:
    - name: New spreadsheet name
  - Returns: Spreadsheet object for chaining

- `getUrl() → string`
  - Gets spreadsheet URL
  - Returns: Spreadsheet's web URL

- `getSheets() → Sheet[]`
  - Gets all sheets
  - Returns: Array of Sheet objects

- `getActiveSheet() → Sheet`
  - Gets active sheet
  - Returns: Currently active Sheet object

- `insertSheet() → Sheet`
  - Creates new sheet
  - Returns: New Sheet object

- `deleteSheet(sheet: Sheet) → Spreadsheet`
  - Deletes sheet
  - Parameters:
    - sheet: Sheet to delete
  - Returns: Spreadsheet object for chaining

- `getRange(a1Notation: string) → Range`
  - Gets range by A1 notation
  - Parameters:
    - a1Notation: A1 notation string (e.g., "A1:B2")
  - Returns: Range object

- `getRange(row: Integer, column: Integer, rows: Integer, columns: Integer) → Range`
  - Gets range by coordinates
  - Parameters:
    - row: Starting row index (1-based)
    - column: Starting column index (1-based)
    - rows: Number of rows
    - columns: Number of columns
  - Returns: Range object

- `getLastRow() → Integer`
  - Gets last row with content
  - Returns: Last row number with content

- `getLastColumn() → Integer`
  - Gets last column with content
  - Returns: Last column number with content

- `deleteRow(rowPosition: Integer) → Spreadsheet`
  - Deletes row
  - Parameters:
    - rowPosition: Row number to delete (1-based)
  - Returns: Spreadsheet object for chaining

- `deleteColumn(columnPosition: Integer) → Spreadsheet`
  - Deletes column
  - Parameters:
    - columnPosition: Column number to delete (1-based)
  - Returns: Spreadsheet object for chaining

- `insertRowAfter(afterPosition: Integer) → Spreadsheet`
  - Inserts row
  - Parameters:
    - afterPosition: Row number to insert after (1-based)
  - Returns: Spreadsheet object for chaining

- `insertColumnAfter(afterPosition: Integer) → Spreadsheet`
  - Inserts column
  - Parameters:
    - afterPosition: Column number to insert after (1-based)
  - Returns: Spreadsheet object for chaining

- `protect() → Protection`
  - Protects sheet/range
  - Returns: Protection object for configuring protection settings

- `sort(column: Integer) → Spreadsheet`
  - Sorts by column
  - Parameters:
    - column: Column number to sort by (1-based)
  - Returns: Spreadsheet object for chaining

##### Sheet Class
Represents a worksheet:
- `getName() → string`
  - Gets sheet name
  - Returns: Sheet display name

- `setName(name: string) → Sheet`
  - Sets sheet name
  - Parameters:
    - name: New sheet name
  - Returns: Sheet object for chaining

- `getRange(a1Notation: string) → Range`
  - Gets range by A1 notation
  - Parameters:
    - a1Notation: A1 notation string (e.g., "A1:B2")
  - Returns: Range object

- `getRange(row: Integer, column: Integer, rows: Integer, columns: Integer) → Range`
  - Gets range by coordinates
  - Parameters:
    - row: Starting row index (1-based)
    - column: Starting column index (1-based)
    - rows: Number of rows
    - columns: Number of columns
  - Returns: Range object

- `getLastRow() → Integer`
  - Gets last row with content
  - Returns: Last row number with content

- `getLastColumn() → Integer`
  - Gets last column with content
  - Returns: Last column number with content

- `getMaxRows() → Integer`
  - Gets total rows
  - Returns: Total number of rows in sheet

- `getMaxColumns() → Integer`
  - Gets total columns
  - Returns: Total number of columns in sheet

- `deleteRows(rowPosition: Integer, howMany: Integer) → Sheet`
  - Deletes rows
  - Parameters:
    - rowPosition: Starting row number (1-based)
    - howMany: Number of rows to delete
  - Returns: Sheet object for chaining

- `deleteColumns(columnPosition: Integer, howMany: Integer) → Sheet`
  - Deletes columns
  - Parameters:
    - columnPosition: Starting column number (1-based)
    - howMany: Number of columns to delete
  - Returns: Sheet object for chaining

- `insertRowsAfter(afterPosition: Integer, howMany: Integer) → Sheet`
  - Inserts rows
  - Parameters:
    - afterPosition: Row number to insert after (1-based)
    - howMany: Number of rows to insert
  - Returns: Sheet object for chaining

- `insertColumnsAfter(afterPosition: Integer, howMany: Integer) → Sheet`
  - Inserts columns
  - Parameters:
    - afterPosition: Column number to insert after (1-based)
    - howMany: Number of columns to insert
  - Returns: Sheet object for chaining

- `clear() → Sheet`
  - Clears sheet content
  - Returns: Sheet object for chaining

- `clearContents() → Sheet`
  - Clears content only
  - Returns: Sheet object for chaining

- `clearFormats() → Sheet`
  - Clears formatting only
  - Returns: Sheet object for chaining

- `autoResizeColumn(columnPosition: Integer) → Sheet`
  - Auto-resizes column
  - Parameters:
    - columnPosition: Column number (1-based)
  - Returns: Sheet object for chaining

- `setColumnWidth(columnPosition: Integer, width: Integer) → Sheet`
  - Sets column width
  - Parameters:
    - columnPosition: Column number (1-based)
    - width: Width in pixels
  - Returns: Sheet object for chaining

- `setRowHeight(rowPosition: Integer, height: Integer) → Sheet`
  - Sets row height
  - Parameters:
    - rowPosition: Row number (1-based)
    - height: Height in pixels
  - Returns: Sheet object for chaining

- `showSheet() → Sheet`
  - Shows sheet
  - Returns: Sheet object for chaining

- `hideSheet() → Sheet`
  - Hides sheet
  - Returns: Sheet object for chaining

- `activate() → Sheet`
  - Makes sheet active
  - Returns: Sheet object for chaining

##### Range Class
Represents a cell range:
- `getValue() → any`
  - Gets cell value
  - Returns: Cell value (string, number, boolean, etc.)

- `setValue(value: any) → Range`
  - Sets cell value
  - Parameters:
    - value: New cell value
  - Returns: Range object for chaining

- `getValues() → any[][]`
  - Gets range values
  - Returns: 2D array of cell values

- `setValues(values: any[][]) → Range`
  - Sets range values
  - Parameters:
    - values: 2D array of values to set
  - Returns: Range object for chaining

- `getFormula() → string`
  - Gets cell formula
  - Returns: Formula string or empty if none

- `setFormula(formula: string) → Range`
  - Sets cell formula
  - Parameters:
    - formula: Formula string
  - Returns: Range object for chaining

- `getFormulas() → string[][]`
  - Gets range formulas
  - Returns: 2D array of formula strings

- `setFormulas(formulas: string[][]) → Range`
  - Sets range formulas
  - Parameters:
    - formulas: 2D array of formula strings
  - Returns: Range object for chaining

- `getBackground() → string`
  - Gets cell background
  - Returns: CSS color value

- `setBackground(color: string) → Range`
  - Sets cell background
  - Parameters:
    - color: CSS color value
  - Returns: Range object for chaining

- `getFontColor() → string`
  - Gets font color
  - Returns: CSS color value

- `setFontColor(color: string) → Range`
  - Sets font color
  - Parameters:
    - color: CSS color value
  - Returns: Range object for chaining

- `getFontWeight() → string`
  - Gets font weight
  - Returns: Font weight value ("normal", "bold", etc.)

- `setFontWeight(weight: string) → Range`
  - Sets font weight
  - Parameters:
    - weight: Font weight value
  - Returns: Range object for chaining

- `getNumberFormat() → string`
  - Gets number format
  - Returns: Number format pattern string

- `setNumberFormat(format: string) → Range`
  - Sets number format
  - Parameters:
    - format: Number format pattern
  - Returns: Range object for chaining

- `merge() → Range`
  - Merges cells
  - Returns: Range object for chaining

- `breakApart() → Range`
  - Unmerges cells
  - Returns: Range object for chaining

- `clear() → Range`
  - Clears range
  - Returns: Range object for chaining

- `clearContent() → Range`
  - Clears content only
  - Returns: Range object for chaining

- `clearFormat() → Range`
  - Clears formatting only
  - Returns: Range object for chaining

- `protect() → Protection`
  - Protects range
  - Returns: Protection object for configuring protection settings

##### BorderStyle Enum
Border style options:
- `SOLID`: Solid line
- `DOTTED`: Dotted line
- `DASHED`: Dashed line
- `DOUBLE`: Double line
- `NONE`: No border

##### DataValidationCriteria Enum
Data validation rules:
- `NUMBER_BETWEEN`: Number in range
- `NUMBER_EQUAL_TO`: Number equals value
- `TEXT_CONTAINS`: Text contains value
- `TEXT_EQUAL_TO`: Text equals value
- `DATE_BETWEEN`: Date in range
- `DATE_EQUAL_TO`: Date equals value
- `CUSTOM_FORMULA`: Custom formula

#### Slides
##### SlidesApp
Main entry point for Google Slides operations:
- `create(name: string) → Presentation`
  - Creates new presentation
  - Parameters:
    - name: Presentation name
  - Returns: New Presentation object

- `openById(id: string) → Presentation`
  - Opens presentation by ID
  - Parameters:
    - id: Presentation ID
  - Returns: Presentation object or null if not found

- `openByUrl(url: string) → Presentation`
  - Opens presentation by URL
  - Parameters:
    - url: Presentation URL
  - Returns: Presentation object or null if not found

- `getActivePresentation() → Presentation`
  - Gets active presentation
  - Returns: Currently active Presentation object

- `getUi() → Ui`
  - Gets presentation UI
  - Returns: UI object for displaying dialogs/menus

- `getSelection() → Selection`
  - Gets current selection
  - Returns: Currently selected elements

##### Presentation Class
Represents a Google Slides presentation:
- `getId() → string`
  - Gets presentation ID
  - Returns: Unique presentation identifier

- `getName() → string`
  - Gets presentation name
  - Returns: Presentation display name

- `setName(name: string) → Presentation`
  - Sets presentation name
  - Parameters:
    - name: New presentation name
  - Returns: Presentation object for chaining

- `getUrl() → string`
  - Gets presentation URL
  - Returns: Presentation's web URL

- `getSlides() → Slide[]`
  - Gets all slides
  - Returns: Array of Slide objects

- `getSelection() → Selection`
  - Gets current selection
  - Returns: Currently selected elements

- `insertSlide(index: Integer) → Slide`
  - Inserts new slide
  - Parameters:
    - index: Zero-based insertion index
  - Returns: New Slide object

- `appendSlide() → Slide`
  - Adds slide at end
  - Returns: New Slide object

- `getSlideById(id: string) → Slide`
  - Gets slide by ID
  - Parameters:
    - id: Slide ID
  - Returns: Slide object or null if not found

- `getLayouts() → Layout[]`
  - Gets slide layouts
  - Returns: Array of Layout objects

- `getMasters() → Master[]`
  - Gets master slides
  - Returns: Array of Master objects

- `saveAndClose() → void`
  - Saves presentation and closes
  - Returns: Nothing

##### Slide Class
Represents a single slide:
- `getObjectId() → string`
  - Gets slide ID
  - Returns: Unique slide identifier

- `getLayout() → Layout`
  - Gets slide layout
  - Returns: Layout object

- `setLayout(layout: Layout) → Slide`
  - Sets slide layout
  - Parameters:
    - layout: Layout object to apply
  - Returns: Slide object for chaining

- `getBackground() → PageBackground`
  - Gets background
  - Returns: PageBackground object

- `setBackground(background: PageBackground) → Slide`
  - Sets background
  - Parameters:
    - background: PageBackground object
  - Returns: Slide object for chaining

- `getPageElements() → PageElement[]`
  - Gets elements
  - Returns: Array of PageElement objects

- `insertShape(type: ShapeType) → Shape`
  - Inserts shape
  - Parameters:
    - type: ShapeType enum value
  - Returns: New Shape object

- `insertTextBox(text: string) → TextBox`
  - Inserts text box
  - Parameters:
    - text: Initial text content
  - Returns: New TextBox object

- `insertImage(blob: BlobSource) → Image`
  - Inserts image
  - Parameters:
    - blob: Image data as blob
  - Returns: New Image object

- `insertTable(rows: Integer, cols: Integer) → Table`
  - Inserts table
  - Parameters:
    - rows: Number of rows
    - cols: Number of columns
  - Returns: New Table object

- `insertVideo(url: string) → Video`
  - Inserts video
  - Parameters:
    - url: Video URL
  - Returns: New Video object

- `duplicate() → Slide`
  - Duplicates slide
  - Returns: New Slide object copy

- `remove() → void`
  - Deletes slide
  - Returns: Nothing

##### Shape Class
Represents a shape element:
- `getObjectId() → string`
  - Gets shape ID
  - Returns: Unique shape identifier

- `getType() → ShapeType`
  - Gets shape type
  - Returns: ShapeType enum value

- `getBorder() → Border`
  - Gets border style
  - Returns: Border object

- `setBorder(border: Border) → Shape`
  - Sets border style
  - Parameters:
    - border: Border object to apply
  - Returns: Shape object for chaining

- `getFill() → Fill`
  - Gets fill style
  - Returns: Fill object

- `setFill(fill: Fill) → Shape`
  - Sets fill style
  - Parameters:
    - fill: Fill object to apply
  - Returns: Shape object for chaining

- `getWidth() → number`
  - Gets width
  - Returns: Width in points

- `setWidth(width: number) → Shape`
  - Sets width
  - Parameters:
    - width: Width in points
  - Returns: Shape object for chaining

- `getHeight() → number`
  - Gets height
  - Returns: Height in points

- `setHeight(height: number) → Shape`
  - Sets height
  - Parameters:
    - height: Height in points
  - Returns: Shape object for chaining

- `getLeft() → number`
  - Gets left position
  - Returns: Left coordinate in points

- `setLeft(left: number) → Shape`
  - Sets left position
  - Parameters:
    - left: Left coordinate in points
  - Returns: Shape object for chaining

- `getTop() → number`
  - Gets top position
  - Returns: Top coordinate in points

- `setTop(top: number) → Shape`
  - Sets top position
  - Parameters:
    - top: Top coordinate in points
  - Returns: Shape object for chaining

- `remove() → void`
  - Removes shape
  - Returns: Nothing

##### TextBox Class
Represents a text box element:
- `getText() → string`
  - Gets text content
  - Returns: Text box content

- `setText(text: string) → TextBox`
  - Sets text content
  - Parameters:
    - text: New text content
  - Returns: TextBox object for chaining

- `clear() → TextBox`
  - Clears text
  - Returns: TextBox object for chaining

- `getTextStyle() → TextStyle`
  - Gets text style
  - Returns: TextStyle object

- `setTextStyle(style: TextStyle) → TextBox`
  - Sets text style
  - Parameters:
    - style: TextStyle object to apply
  - Returns: TextBox object for chaining

- `getFontFamily() → string`
  - Gets font family
  - Returns: Font family name

- `setFontFamily(font: string) → TextBox`
  - Sets font family
  - Parameters:
    - font: Font family name
  - Returns: TextBox object for chaining

- `getFontSize() → number`
  - Gets font size
  - Returns: Font size in points

- `setFontSize(size: number) → TextBox`
  - Sets font size
  - Parameters:
    - size: Font size in points
  - Returns: TextBox object for chaining

- `isBold() → boolean`
  - Checks if bold
  - Returns: true if bold, false otherwise

- `setBold(bold: boolean) → TextBox`
  - Sets bold style
  - Parameters:
    - bold: Whether to make text bold
  - Returns: TextBox object for chaining

- `isItalic() → boolean`
  - Checks if italic
  - Returns: true if italic, false otherwise

- `setItalic(italic: boolean) → TextBox`
  - Sets italic style
  - Parameters:
    - italic: Whether to make text italic
  - Returns: TextBox object for chaining

##### Table Class
Represents a table element:
- `getNumRows() → Integer`
  - Gets row count
  - Returns: Number of rows in table

- `getNumColumns() → Integer`
  - Gets column count
  - Returns: Number of columns in table

- `getCell(row: Integer, col: Integer) → TableCell`
  - Gets cell at coordinates
  - Parameters:
    - row: Zero-based row index
    - col: Zero-based column index
  - Returns: TableCell object

- `insertRow(index: Integer) → Table`
  - Inserts row at index
  - Parameters:
    - index: Zero-based insertion index
  - Returns: Table object for chaining

- `insertColumn(index: Integer) → Table`
  - Inserts column at index
  - Parameters:
    - index: Zero-based insertion index
  - Returns: Table object for chaining

- `deleteRow(index: Integer) → Table`
  - Deletes row at index
  - Parameters:
    - index: Zero-based row index
  - Returns: Table object for chaining

- `deleteColumn(index: Integer) → Table`
  - Deletes column at index
  - Parameters:
    - index: Zero-based column index
  - Returns: Table object for chaining

##### Enums
###### PageType
Slide types:
- `SLIDE`: Regular slide
- `LAYOUT`: Layout slide
- `MASTER`: Master slide
- `NOTES`: Speaker notes

###### ShapeType
Shape types:
- `RECTANGLE`: Rectangle shape
- `ROUND_RECTANGLE`: Rounded rectangle
- `ELLIPSE`: Ellipse/circle
- `TRIANGLE`: Triangle
- `ARROW`: Arrow shape
- `CALLOUT`: Callout bubble
- `STAR`: Star shape
- `LINE`: Straight line

### Utility Services

#### Base Services
- Browser
- Logger
- Session
- Console

#### Cache Service
- CacheService
- Cache operations

#### HTML Service
- HtmlService
- HTML template processing
- Client-side APIs

#### Lock Service
- LockService
- Concurrent access control

#### Properties Service
- PropertiesService
- Script/user/document properties

#### URL Fetch Service
- UrlFetchApp
- External HTTP requests

#### XML Service
- XmlService
- XML parsing and creation

#### Mail Service
- MailApp
- Email operations

## Project Resources

- Manifest configuration
- Automation triggers
- Quotas and limits
- Project settings

## Best Practices

1. Use built-in services when available instead of external APIs
2. Enable only needed advanced services
3. Handle quotas and limits appropriately
4. Follow security best practices
5. Use appropriate authorization scopes

## Additional Resources

- [Apps Script Dashboard](https://script.google.com/)
- [Google Cloud Console](https://console.cloud.google.com/)
- [Developer Documentation](https://developers.google.com/apps-script)
- [Community Support](https://developers.google.com/apps-script/support)
