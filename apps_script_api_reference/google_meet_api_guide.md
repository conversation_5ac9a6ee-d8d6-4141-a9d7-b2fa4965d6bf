# Google Meet API Guide

This guide provides an overview and links to official documentation for integrating with Google Meet, primarily focusing on its raw REST API and how conferencing is managed within Google Apps Script.

## Section 1: Google Meet Integration within Google Apps Script

Direct interaction with Google Meet via a dedicated "MeetApp" service or a specific "Meet Advanced Service" is not available in Google Apps Script in the same way as for Calendar or Gmail. Instead, Google Meet conferencing details are typically managed as part of Google Calendar events.

### Managing Meet Conferencing via Calendar Services

When creating or modifying calendar events using Apps Script, you can add Google Meet conferencing details.

*   **Using `CalendarApp` Service (Built-in):**
    *   When creating an event (`Calendar.createEvent()`), you can often have Meet conferencing added automatically based on domain settings or by using specific options if available.
    *   Refer to the `CalendarEvent` class documentation for methods related to conference data: [CalendarEvent Reference](https://developers.google.com/apps-script/reference/calendar/calendar-event) (Look for methods like `addGuest()` which might trigger conferencing, or specific conferencing methods if added in the future).
*   **Using Advanced Calendar API Service:**
    *   The Advanced Calendar API service provides more granular control over event creation, including specifying conference data solutions. You would use the `Events: insert` or `Events: patch` methods of the underlying Google Calendar REST API.
    *   **ConferenceData:** The key is the `conferenceData` object within an event resource, where you can specify the `createRequest` field to add a new conference, or provide existing conference details.
    *   **Official Documentation:** [Advanced Calendar API Service Reference](https://developers.google.com/apps-script/advanced/calendar)
    *   **Relevant REST API Docs for Conferencing:** [Calendar API - Events Resource - conferenceData](https://developers.google.com/calendar/api/v3/reference/events#conferenceData)

### Google Workspace Events API (Advanced Service)

For subscribing to notifications about Google Meet events (e.g., when a meeting starts or ends), you might use the Google Workspace Events API.

*   **Purpose:** Allows you to subscribe to real-time events in Google Workspace, including some related to Meet.
*   **Official Documentation:** [Advanced Events API Service Reference](https://developers.google.com/apps-script/advanced/events)
*   **Relevant REST API Docs:** [Google Workspace Events API Overview](https://developers.google.com/workspace/events/overview)

## Section 2: Raw Google Meet REST API

The Google Meet REST API allows developers to create and manage Google Meet meetings programmatically. This is useful for integrating Meet scheduling and management into custom applications.

*   **Purpose:** Provides HTTP endpoints for creating meeting spaces, getting meeting space details, and potentially other management functions.
*   **Official Documentation Hub:** [Google Meet API Overview](https://developers.google.com/meet/api/guides/overview)
*   **Client Libraries:** Google provides client libraries for various languages. See the "Client Libraries" section on the [Meet API overview page](https://developers.google.com/meet/api/guides/overview).

### Key Concepts & Resources:

*   **Authentication & Authorization:**
    *   The Meet API uses OAuth 2.0 for authorization.
    *   **Documentation:** [Authenticate and authorize Meet API requests](https://developers.google.com/meet/api/guides/auth)

*   **Spaces (Meetings):**
    *   A `Space` resource represents a virtual meeting room in Google Meet.
    *   **Resource Representation:** [`spaces`](https://developers.google.com/meet/api/reference/rest/v1/spaces)
    *   **Key Operations:**
        *   Create a space: [`spaces.create`](https://developers.google.com/meet/api/reference/rest/v1/spaces/create)
            *   **Key Parameters:** `requestBody` (optional, can be empty to create a space with default settings, or specify `SpaceConfig` like `accessType`).
            *   **Returns:** The created `Space` resource, including the `meetingUri` and `meetingCode`.
        *   Get a space: [`spaces.get`](https://developers.google.com/meet/api/reference/rest/v1/spaces/get)
            *   **Key Parameters:** `name` (space ID, e.g., `spaces/{space_id}`).
            *   **Returns:** The `Space` resource.
        *   Update a space: [`spaces.patch`](https://developers.google.com/meet/api/reference/rest/v1/spaces/patch)
            *   **Key Parameters:** `name` (space ID), `requestBody` (Space resource with fields to update, e.g., `spaceConfig`), `updateMask`.
            *   **Returns:** The updated `Space` resource.
        *   End a meeting (space): [`spaces.endActiveMeeting`](https://developers.google.com/meet/api/reference/rest/v1/spaces/endActiveMeeting)
             *   **Key Parameters:** `name` (space ID).
             *   **Returns:** An empty response on success.

*   **Conference Records:**
    *   A `ConferenceRecord` resource provides information about past conferences, such as start/end times and participant details.
    *   **Resource Representation:** [`conferenceRecords`](https://developers.google.com/meet/api/reference/rest/v1/conferenceRecords)
    *   **Key Operations:**
        *   Get a conference record: [`conferenceRecords.get`](https://developers.google.com/meet/api/reference/rest/v1/conferenceRecords/get)
            *   **Key Parameters:** `name` (conference record ID, e.g., `conferenceRecords/{conference_record_id}`).
            *   **Returns:** The `ConferenceRecord` resource.
        *   List conference records: [`conferenceRecords.list`](https://developers.google.com/meet/api/reference/rest/v1/conferenceRecords/list)
            *   **Key Parameters:** `pageSize`, `pageToken`, `filter` (e.g., by space ID or time range).
            *   **Returns:** A list of `ConferenceRecord` resources and a `nextPageToken`.

*   **Participants & Participant Sessions (within Conference Records):**
    *   These resources provide details about who joined a conference and their session specifics.
    *   **Resource Representation:**
        *   [`conferenceRecords.participants`](https://developers.google.com/meet/api/reference/rest/v1/conferenceRecords.participants)
        *   [`conferenceRecords.participants.participantSessions`](https://developers.google.com/meet/api/reference/rest/v1/conferenceRecords.participants.participantSessions)
    *   **Key Operations (Listing and Getting):**
        *   List participants: [`conferenceRecords.participants.list`](https://developers.google.com/meet/api/reference/rest/v1/conferenceRecords.participants/list)
        *   Get a participant: [`conferenceRecords.participants.get`](https://developers.google.com/meet/api/reference/rest/v1/conferenceRecords.participants/get)
        *   List participant sessions: [`conferenceRecords.participants.participantSessions.list`](https://developers.google.com/meet/api/reference/rest/v1/conferenceRecords.participants.participantSessions/list)
        *   Get a participant session: [`conferenceRecords.participants.participantSessions.get`](https://developers.google.com/meet/api/reference/rest/v1/conferenceRecords.participants.participantSessions/get)
            *   **Key Parameters:** Typically `parent` (conference record or participant ID), `name` (specific resource ID), `pageSize`, `pageToken`, `filter`.
            *   **Returns:** The respective resource or list of resources.

*   **Important Note:** The official documentation linked throughout this section is the authoritative source for all details regarding the raw Meet REST API, including specific request/response formats, all available parameters, authentication scopes, and usage limits.
