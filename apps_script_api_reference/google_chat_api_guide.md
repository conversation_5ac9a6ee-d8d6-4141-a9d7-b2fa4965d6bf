# Google Chat API Guide

This guide provides an overview and links to official documentation for using Google Chat APIs, both within Google Apps Script (for building Chat apps) and via its raw REST API (for broader integrations).

## Section 1: Google Chat Integration within Google Apps Script

Google Apps Script is a common platform for building Google Chat apps (bots). This is primarily done using the Card Service for UI and handling events from Chat. While there isn't a simple "ChatApp" service like `GmailApp`, interaction is often event-driven or uses the Advanced Chat API service.

### Building Chat Apps

*   **Event-Driven Model:** Chat apps in Apps Script typically respond to events (like messages or users joining/leaving spaces) defined in the [app's manifest](https://developers.google.com/workspace/chat/develop-apps-script#configure_your_apps_script_project).
*   **Card Service:** The `CardService` in Apps Script is used to create and manage the user interface elements (cards, widgets, dialogs) that your Chat app displays.
    *   **Official Documentation:** [Card Service Reference](https://developers.google.com/apps-script/reference/card-service)
*   **Responding to Interactions:** Your Apps Script functions handle user interactions with these cards.

### Advanced Chat API Service

The Advanced Chat API service in Apps Script provides a wrapper around the Google Chat REST API. This allows your Apps Script code (including Chat apps) to perform actions like creating spaces, sending messages, managing members, and more, beyond just responding to interaction events.

*   **Purpose:** Enables Apps Script projects to directly call the Google Chat REST API.
*   **Official Documentation:** [Advanced Chat API Service Reference](https://developers.google.com/apps-script/advanced/chat)
*   **Enabling the Service:** Refer to the [Enable Advanced Services guide](https://developers.google.com/apps-script/guides/services/advanced#enable_advanced_services).
*   **Common Use Cases in Apps Script (examples of what the Advanced Service enables):**
    *   **Sending a message to a space:** `Chat.Spaces.Messages.create(messageResource, parentSpaceName)`
    *   **Creating a new space:** `Chat.Spaces.create(spaceResource)`
    *   **Listing members of a space:** `Chat.Spaces.Members.list(parentSpaceName)`
    *   **Getting message details:** `Chat.Spaces.Messages.get(messageName)`
    *   (Note: The exact Apps Script syntax and available methods are detailed in the official "Advanced Chat API Service Reference" linked above.)

## Section 2: Raw Google Chat REST API

The Google Chat REST API allows any application (not just Apps Script based Chat apps) to integrate with Google Chat. This enables a wide range of functionalities for automating and extending Chat.

*   **Purpose:** Provides HTTP endpoints for managing Chat resources such as messages, spaces, members, and reactions. It's used for building Chat apps, bots, and integrating Chat with other enterprise systems.
*   **Official Documentation Hub:** [Google Chat API Overview](https://developers.google.com/chat/api/guides)
*   **Client Libraries:** Google provides client libraries for various languages (Java, Python, Node.js, Go, .NET, PHP, Ruby). See [Chat API Client Libraries](https://developers.google.com/chat/api/guides/libraries).

### Key Concepts & Resources:

*   **Authentication & Authorization:**
    *   Chat API supports both user authentication (OAuth 2.0) for actions on behalf of a user, and app authentication (service accounts) for Chat apps acting independently.
    *   **Documentation:** [Authenticate and authorize Chat apps and API requests](https://developers.google.com/chat/api/guides/auth)

*   **Spaces:**
    *   Spaces are the central places where people and apps can share messages, files, and tasks. They can be direct messages (DMs), group chats, or named spaces.
    *   **Resource Representation:** [`spaces`](https://developers.google.com/chat/api/reference/rest/v1/spaces)
    *   **Key Operations:**
        *   Create spaces: [`spaces.create`](https://developers.google.com/chat/api/reference/rest/v1/spaces/create)
            *   **Key Parameters:** `requestBody` (Space resource, e.g., `displayName`, `spaceType`), `requestId`.
            *   **Returns:** The created `Space` resource.
        *   Get space details: [`spaces.get`](https://developers.google.com/chat/api/reference/rest/v1/spaces/get)
            *   **Key Parameters:** `name` (space ID).
            *   **Returns:** The `Space` resource.
        *   List spaces: [`spaces.list`](https://developers.google.com/chat/api/reference/rest/v1/spaces/list)
            *   **Key Parameters:** `pageSize`, `pageToken`, `filter`.
            *   **Returns:** A list of `Space` resources and a `nextPageToken`.
        *   Update spaces: [`spaces.patch`](https://developers.google.com/chat/api/reference/rest/v1/spaces/patch)
            *   **Key Parameters:** `name` (space ID), `requestBody` (Space resource with fields to update), `updateMask`.
            *   **Returns:** The updated `Space` resource.
        *   Delete spaces: [`spaces.delete`](https://developers.google.com/chat/api/reference/rest/v1/spaces/delete) (typically for app-created spaces)
            *   **Key Parameters:** `name` (space ID).
            *   **Returns:** An empty response on success.
        *   Find direct message space: [`spaces.findDirectMessage`](https://developers.google.com/chat/api/reference/rest/v1/spaces/findDirectMessage)
            *   **Key Parameters:** `name` (user ID, e.g., `users/<EMAIL>`).
            *   **Returns:** The `Space` resource representing the DM.
    *   **Guides:** [Work with spaces](https://developers.google.com/chat/api/guides/v1/spaces)

*   **Messages:**
    *   Messages are the content shared within spaces. They can be simple text or include rich formatting with cards.
    *   **Resource Representation:** [`spaces.messages`](https://developers.google.com/chat/api/reference/rest/v1/spaces.messages)
    *   **Key Operations:**
        *   Create/send messages: [`spaces.messages.create`](https://developers.google.com/chat/api/reference/rest/v1/spaces.messages/create)
            *   **Key Parameters:** `parent` (space ID), `requestBody` (Message resource including `text` or `cardsV2`), `messageReplyOption`, `threadKey`, `requestId`.
            *   **Returns:** The created `Message` resource.
        *   Get message details: [`spaces.messages.get`](https://developers.google.com/chat/api/reference/rest/v1/spaces.messages/get)
            *   **Key Parameters:** `name` (message ID).
            *   **Returns:** The `Message` resource.
        *   List messages in a space: [`spaces.messages.list`](https://developers.google.com/chat/api/reference/rest/v1/spaces.messages/list)
            *   **Key Parameters:** `parent` (space ID), `pageSize`, `pageToken`, `filter`, `orderBy`.
            *   **Returns:** A list of `Message` resources and a `nextPageToken`.
        *   Update messages: [`spaces.messages.update`](https://developers.google.com/chat/api/reference/rest/v1/spaces.messages/update)
            *   **Key Parameters:** `name` (message ID), `requestBody` (Message resource with fields to update, e.g., `text` or `cardsV2`), `updateMask` (specifies which fields to update), `allowMissing` (if true, creates message if it doesn't exist).
            *   **Returns:** The updated `Message` resource.
        *   Delete messages: [`spaces.messages.delete`](https://developers.google.com/chat/api/reference/rest/v1/spaces.messages/delete)
            *   **Key Parameters:** `name` (message ID), `force` (if true, allows deletion of last message in a space if user has permission).
            *   **Returns:** An empty response on success.
    *   **Guides:**
        *   [Send messages](https://developers.google.com/chat/api/guides/v1/messages/create)
        *   [Format messages](https://developers.google.com/chat/api/guides/message-formats) (including cards)
        *   [Receive and respond to messages (for Chat apps)](https://developers.google.com/chat/api/guides/v1/messages/receive)

*   **Memberships:**
    *   Memberships represent the relationship between a user or a Chat app and a space.
    *   **Resource Representation:** [`spaces.members`](https://developers.google.com/chat/api/reference/rest/v1/spaces.members)
    *   **Key Operations:**
        *   Add members (invite): [`spaces.members.create`](https://developers.google.com/chat/api/reference/rest/v1/spaces.members/create)
            *   **Key Parameters:** `parent` (space ID), `requestBody` (Membership resource, typically specifying the `member.name` like `users/{user_id}` or `apps/{app_id}`).
            *   **Returns:** The created `Membership` resource.
        *   Get membership details: [`spaces.members.get`](https://developers.google.com/chat/api/reference/rest/v1/spaces.members/get)
            *   **Key Parameters:** `name` (membership ID, e.g., `spaces/{space_id}/members/{member_id}`).
            *   **Returns:** The `Membership` resource.
        *   List members in a space: [`spaces.members.list`](https://developers.google.com/chat/api/reference/rest/v1/spaces.members/list)
            *   **Key Parameters:** `parent` (space ID), `pageSize`, `pageToken`, `filter`, `showGroups`, `showInvited`.
            *   **Returns:** A list of `Membership` resources and a `nextPageToken`.
        *   Remove members: [`spaces.members.delete`](https://developers.google.com/chat/api/reference/rest/v1/spaces.members/delete)
            *   **Key Parameters:** `name` (membership ID).
            *   **Returns:** An empty response on success.
    *   **Guides:** [Manage space members](https://developers.google.com/chat/api/guides/v1/members)

*   **Reactions:**
    *   Reactions are emojis added to messages.
    *   **Resource Representation:** [`spaces.messages.reactions`](https://developers.google.com/chat/api/reference/rest/v1/spaces.messages.reactions)
    *   **Key Operations:**
        *   Add a reaction: [`spaces.messages.reactions.create`](https://developers.google.com/chat/api/reference/rest/v1/spaces.messages.reactions/create)
            *   **Key Parameters:** `parent` (message ID), `requestBody` (Reaction resource, typically specifying `emoji.unicode`).
            *   **Returns:** The created `Reaction` resource.
        *   List reactions for a message: [`spaces.messages.reactions.list`](https://developers.google.com/chat/api/reference/rest/v1/spaces.messages.reactions/list)
            *   **Key Parameters:** `parent` (message ID), `pageSize`, `pageToken`, `filter`.
            *   **Returns:** A list of `Reaction` resources and a `nextPageToken`.
        *   Delete a reaction: [`spaces.messages.reactions.delete`](https://developers.google.com/chat/api/reference/rest/v1/spaces.messages.reactions/delete)
            *   **Key Parameters:** `name` (reaction ID, e.g., `spaces/{space_id}/messages/{message_id}/reactions/{reaction_id}`).
            *   **Returns:** An empty response on success.
    *   **Guides:** [Manage reactions to a message](https://developers.google.com/chat/api/guides/v1/reactions)

*   **Media & Attachments:**
    *   The API allows uploading files as attachments to messages and downloading them.
    *   **Resource Representation:** [`media`](https://developers.google.com/chat/api/reference/rest/v1/media) (for uploads), [`spaces.messages.attachments`](https://developers.google.com/chat/api/reference/rest/v1/spaces.messages.attachments) (for retrieving attachment metadata)
    *   **Key Operations:**
        *   Upload media: [`media.upload`](https://developers.google.com/chat/api/reference/rest/v1/media/upload)
            *   **Key Parameters:** `parent` (space ID where message with attachment will be posted), `filename` (in request body, if sending a message simultaneously). The actual file content is sent as the request body.
            *   **Returns:** An `Attachment` resource containing the `attachmentDataRef.resourceName` (which is the media resource name).
        *   Download media/attachment:
            *   Using `media.download`: [`media.download`](https://developers.google.com/chat/api/reference/rest/v1/media/download?customBytes=true)
                *   **Key Parameters:** `resourceName` (media resource name, obtained from an `Attachment` resource).
                *   **Returns:** The raw bytes of the file.
            *   Using `spaces.messages.attachments.get`: [`spaces.messages.attachments.get`](https://developers.google.com/chat/api/reference/rest/v1/spaces.messages.attachments/get)
                *   **Key Parameters:** `name` (attachment ID, e.g., `spaces/{space_id}/messages/{message_id}/attachments/{attachment_id}`).
                *   **Returns:** An `Attachment` resource containing metadata and `attachmentDataRef` (which includes the media resource name for downloading).
    *   **Guides:** [Upload & download media as attachments](https://developers.google.com/chat/api/guides/v1/media-attachments)

*   **Events API (Google Workspace Events API):**
    *   To receive real-time notifications about events in Google Chat (e.g., new messages, new members, reactions), you subscribe using the Google Workspace Events API.
    *   **Chat Event Types:** [Supported event types for Google Chat](https://developers.google.com/workspace/events/guides/supported-events#chat)
    *   **Guides:** [Subscribe to Google Chat events](https://developers.google.com/workspace/events/guides/subscribe-google-chat-events)

*   **Important Note:** The official documentation linked throughout this section is the authoritative source for all details regarding the raw Chat REST API, including specific request/response formats, all available parameters, authentication scopes, and usage limits.
