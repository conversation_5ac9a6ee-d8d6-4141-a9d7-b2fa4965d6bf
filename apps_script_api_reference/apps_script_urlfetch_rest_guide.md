# Calling REST APIs with UrlFetch<PERSON>pp in Google Apps Script

Google Apps Script provides the `UrlFetchApp` service to make HTTP/HTTPS requests to external services and REST APIs. This guide covers the basics of using `UrlFetchApp` for this purpose.

**Official Documentation:**
*   [UrlFetchApp Service Reference](https://developers.google.com/apps-script/reference/url-fetch/url-fetch-app)
*   [HTTPResponse Class Reference](https://developers.google.com/apps-script/reference/url-fetch/http-response)
*   [Guide to External APIs](https://developers.google.com/apps-script/guides/services/external)

## 1. Introduction to `UrlFetchApp`

`UrlFetchApp` allows your scripts to:
*   Fetch resources from URLs (e.g., get data from a public API).
*   Send data to URLs (e.g., post data to a REST endpoint).
*   Interact with any HTTP/HTTPS based API.

## 2. Making Basic Requests

The core method is `UrlFetchApp.fetch(url, params)`.

*   `url`: The endpoint URL of the REST API.
*   `params`: An optional JavaScript object specifying advanced parameters for the request.

### GET Request (Default)

If `params` is omitted or doesn't specify a `method`, a GET request is made.

```javascript
function simpleGetRequest() {
  const url = 'https://api.example.com/data';
  try {
    const response = UrlFetchApp.fetch(url);
    const responseCode = response.getResponseCode();
    const responseBody = response.getContentText();

    if (responseCode === 200) {
      const data = JSON.parse(responseBody);
      Logger.log(data);
    } else {
      Logger.log(`Error: ${responseCode} - ${responseBody}`);
    }
  } catch (e) {
    Logger.log(`Exception: ${e}`);
  }
}
```

### POST, PUT, DELETE, PATCH Requests

Specify the HTTP method in the `params` object.

```javascript
function postRequestExample() {
  const url = 'https://api.example.com/submit';
  const payload = {
    name: 'Test Item',
    value: 123
  };

  const options = {
    'method': 'post',
    'contentType': 'application/json', // Important for sending JSON
    'payload': JSON.stringify(payload) // Payload must be a string
  };

  try {
    const response = UrlFetchApp.fetch(url, options);
    // ... handle response as in GET example
    Logger.log(response.getContentText());
  } catch (e) {
    Logger.log(`Exception: ${e}`);
  }
}
```
*   For **PUT**, set `method: 'put'`.
*   For **DELETE**, set `method: 'delete'`. (Payload is often not needed for DELETE).
*   For **PATCH**, set `method: 'patch'`.

## 3. Setting Headers

Headers are set within the `headers` property of the `params` object.

```javascript
const options = {
  'method': 'get',
  'headers': {
    'Authorization': 'Bearer YOUR_ACCESS_TOKEN', // Example for OAuth2
    'X-Custom-Header': 'MyValue',
    'Accept': 'application/json'
  }
};
// const response = UrlFetchApp.fetch(url, options);
```

### Common Headers:

*   `Content-Type`: Specifies the format of the request body (payload).
    *   `application/json` for JSON data.
    *   `application/x-www-form-urlencoded` for form data.
*   `Authorization`: Used for sending authentication credentials (e.g., API keys, OAuth tokens).
*   `Accept`: Tells the server what content types the client can understand in the response.

## 4. Handling Payloads (Request Body)

The `payload` parameter in `options` is used for POST, PUT, and PATCH requests.
*   It **must be a string**. If you have a JavaScript object, use `JSON.stringify()` for JSON APIs.
*   For `application/x-www-form-urlencoded`, the payload should be a query string like `'key1=value1&key2=value2'`.

## 5. Handling Responses

The `UrlFetchApp.fetch()` method returns an `HTTPResponse` object.

*   `getResponseCode()`: Returns the HTTP status code (e.g., 200 for OK, 404 for Not Found).
*   `getContentText()`: Returns the response body as a string.
    *   If the response is JSON, use `JSON.parse(response.getContentText())`.
*   `getBlob()`: Returns the response body as a `Blob` (useful for binary data like images).
*   `getAllHeaders()`: Returns an object containing all response headers.
*   `getHeaders()`: Returns an object containing all response headers (same as `getAllHeaders`).

## 6. Authentication

### API Keys

Some APIs use API keys for authentication. These are typically sent as a query parameter in the URL or as a custom HTTP header (e.g., `X-API-Key`).

```javascript
// As a query parameter
// const url = 'https://api.example.com/data?apiKey=YOUR_API_KEY';

// As a header
const optionsWithApiKey = {
  'headers': {
    'X-API-Key': 'YOUR_API_KEY'
  }
};
```

### OAuth2

For calling Google APIs or other services secured with OAuth2:

1.  **Ensure Scopes are Added**: If calling Google APIs, your Apps Script project manifest (`appsscript.json`) must include the necessary OAuth scopes for the API you're trying to access.
    ```json
    {
      "timeZone": "America/New_York",
      "dependencies": {},
      "exceptionLogging": "STACKDRIVER",
      "runtimeVersion": "V8",
      "oauthScopes": [
        "https://www.googleapis.com/auth/script.external_request", // For UrlFetchApp itself
        "https://www.googleapis.com/auth/userinfo.email", // Example scope
        "https://www.googleapis.com/auth/drive.readonly" // Example scope for Drive API
        // Add other scopes required by the target API
      ]
    }
    ```

2.  **Get the OAuth Token**: Use `ScriptApp.getOAuthToken()` to get an access token for the current user, valid for the scopes declared in the manifest.

    ```javascript
    function callGoogleApiWithOAuth() {
      const token = ScriptApp.getOAuthToken();
      const googleApiUrl = 'https://www.googleapis.com/drive/v3/files?pageSize=10'; // Example Drive API

      const options = {
        'method': 'get',
        'headers': {
          'Authorization': 'Bearer ' + token,
          'Accept': 'application/json'
        },
        'muteHttpExceptions': true // Recommended for better error handling
      };

      try {
        const response = UrlFetchApp.fetch(googleApiUrl, options);
        const responseCode = response.getResponseCode();
        const responseBody = response.getContentText();

        if (responseCode === 200) {
          const data = JSON.parse(responseBody);
          Logger.log(JSON.stringify(data, null, 2));
        } else {
          Logger.log(`Error calling API: ${responseCode} - ${responseBody}`);
          // For 401/403, token might be invalid/expired or scopes missing.
        }
      } catch (e) {
        Logger.log(`Exception during API call: ${e}`);
      }
    }
    ```
    **Note**: `ScriptApp.getOAuthToken()` only works for Google APIs. For third-party OAuth2 services, you'd typically implement the full OAuth2 flow (redirection, token exchange) which is more complex and might involve using HTML Service or external libraries.

## 7. Error Handling

*   **HTTP Status Codes**: Always check `response.getResponseCode()`. Codes like 4xx (client errors) or 5xx (server errors) indicate problems.
*   **`muteHttpExceptions`**: By default, if the HTTP response code indicates an error (e.g., 404), `UrlFetchApp.fetch()` throws an exception. Setting `muteHttpExceptions: true` in the `params` object prevents this, allowing your script to handle the error code directly from the `HTTPResponse` object. This is generally recommended for robust error handling.
*   **`try...catch` Blocks**: Wrap `UrlFetchApp.fetch()` calls in `try...catch` blocks to handle network issues or other unexpected exceptions.

```javascript
const options = {
  'method': 'get',
  'muteHttpExceptions': true // Important for manual error checking
};

try {
  const response = UrlFetchApp.fetch('https://api.example.com/nonexistent', options);
  const responseCode = response.getResponseCode();

  if (responseCode === 200) {
    // Success
  } else if (responseCode === 404) {
    Logger.log('Resource not found.');
  } else {
    Logger.log(`Unhandled error: ${responseCode} - ${response.getContentText()}`);
  }
} catch (e) {
  // Handles network errors or if muteHttpExceptions was false and an HTTP error occurred
  Logger.log(`Fetch failed: ${e}`);
}
```

## 8. Quotas and Limitations

`UrlFetchApp` has quotas, such as the number of calls per day and the total runtime.
*   **Official Quotas Page:** [Apps Script Quotas - UrlFetch](https://developers.google.com/apps-script/guides/services/quotas#urlfetch)

Be mindful of these limits, especially when making many calls or dealing with large responses.

This guide provides a foundational understanding of using `UrlFetchApp` to interact with REST APIs from Google Apps Script. Always refer to the specific API's documentation for its endpoints, required parameters, authentication methods, and rate limits.
