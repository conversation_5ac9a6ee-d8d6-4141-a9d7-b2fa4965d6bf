# Google Sheets API Documentation for Google Apps Script

This document compiles information from the official Google Sheets API and Google Apps Script documentation.

## Overview of Google Sheets for Developers

This section provides a general overview of how developers can enhance Google Sheets with add-ons, automate it with Apps Script, and connect services using REST APIs.

### Enhance the Google Sheets experience

Insert interactive content, powered by your account data or an external service, with **add-ons**.

-   Create an interface for customizing tables in Sheets.
-   Display an immersive Mail Merge tool.
-   Build a tool for creating better charts and visualizations.

[View documentation](https://developers.google.com/workspace/add-ons/editors/gsao) [Learn about add-ons](https://developers.google.com/workspace/add-ons)

### Automate Google Sheets with simple code

Anyone can use **Apps Script** to automate and enhance Google Sheets in a web-based, low-code environment.

-   Create custom functions or macros in Sheets.
-   Add custom menus, sidebars and dialogs to Sheets.
-   Connect Sheets to other Google Workspace apps or third-party services.

[View documentation](https://developers.google.com/apps-script/guides/sheets) [Learn about Apps Script](https://developers.google.com/apps-script)

### Connect your service to Google Sheets

Use the REST APIs below to interact programmatically with Google Sheets.

### Sheets API

**Access and update spreadsheets programmatically**, just like any other user.

[View documentation](https://developers.google.com/workspace/sheets/api/guides/concepts) [Try it out](https://developers.google.com/workspace/sheets/api/reference/rest/v4/spreadsheets/get?apix=true)

---

## Google Apps Script Quickstart

This section provides a detailed quickstart guide on how to create an Apps Script that makes requests to the Google Sheets API.

Quickstarts explain how to set up and run an app that calls a Google Workspace API.

Google Workspace quickstarts use the API client libraries to handle some details of the authentication and authorization flow. We recommend that you use the client libraries for your own apps. This quickstart uses a simplified authentication approach that is appropriate for a testing environment. For a production environment, we recommend learning about [authentication and authorization](/workspace/guides/auth-overview) before [choosing the access credentials](/workspace/guides/create-credentials#choose_the_access_credential_that_is_right_for_you) that are appropriate for your app.

Create a [Google Apps Script](/apps-script/overview) that makes requests to the Google Sheets API.

### Objectives

-   Configure the environment.
-   Create and configure the script.
-   Run the script.

### Prerequisites

-   A Google Account

-   Access to Google Drive

### Create the script

1.  Create a new script by going to [script.google.com/create](https://script.google.com/create).
2.  Replace the contents of the script editor with the following code:

sheets/quickstart/quickstart.gs

[View on GitHub](https://github.com/googleworkspace/apps-script-samples/blob/main/sheets/quickstart/quickstart.gs)

/\*\*
 \* Creates a Sheets API service object and prints the names and majors of
 \* students in a sample spreadsheet:
 \* https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit
 \* @see https://developers.google.com/sheets/api/reference/rest/v4/spreadsheets.values/get
 \*/
functionlogNamesAndMajors(){
constspreadsheetId\='1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms';
constrangeName\='ClassData!A2:E';
try{
// Get the values from the spreadsheet using spreadsheetId and range.
constvalues\=Sheets.Spreadsheets.Values.get(spreadsheetId,rangeName).values;
//  Print the values from spreadsheet if values are available.
if(!values){
console.log('Nodatafound.');
return;
}
console.log('Name,Major:');
for(constrowinvalues){
// Print columns A and E, which correspond to indices 0 and 4.
console.log('\-%s,%s',values\[row\]\[0\],values\[row\]\[4\]);
}
}catch(err){
// TODO (developer) - Handle Values.get() exception from Sheet API
console.log(err.message);
}
}

3.  Click Save .
4.  Click **Untitled project**, type **Quickstart**, and click **Rename**.

### Configure the script

#### Enable the Google Sheets API

Open the Apps Script project.

1.  Click **Editor** code.
2.  Next to **Services**, click Add a service add .
3.  Select Sheets API and click **Add**.

### Run the sample

In the Apps Script editor, click **Run**.

The first time you run the sample, it prompts you to authorize access:

1.  Click **Review permissions**.
2.  Choose an account.
3.  Click **Allow**.

The script's execution log appears at the bottom of the window.

done It worked!

**Great!** Check out the further reading section below to learn more.

warning There was a problem

**Bummer**, let us know what went wrong. Check out our troubleshooting section below for some common errors and solutions. If you have found a bug in the code, [report the issue on GitHub](https://github.com/googleworkspace/apps-script-samples/issues) or submit a pull request.

### Next steps

-   [Google Apps Script Advanced Services documentation](/apps-script/guides/services/advanced)
-   [Troubleshoot authentication and authorization issues](/workspace/sheets/api/troubleshoot-authentication-authorization)
-   [Sheets API reference documentation](/workspace/sheets/api/reference/rest)

Send feedback

Except as otherwise noted, the content of this page is licensed under the [Creative Commons Attribution 4.0 License](https://creativecommons.org/licenses/by/4.0/), and code samples are licensed under the [Apache 2.0 License](https://www.apache.org/licenses/LICENSE-2.0). For details, see the [Google Developers Site Policies](https://developers.google.com/site-policies). Java is a registered trademark of Oracle and/or its affiliates.

Last updated 2025-05-07 UTC.

---

## Advanced Google Services

This section provides information on using Advanced Google Services in Google Apps Script.

bookmark\_borderbookmark Stay organized with collections Save and categorize content based on your preferences.

-   On this page
-   Advanced services or HTTP?
-   Requirements
-   Enable advanced services
-   How method signatures are determined
-   Support for advanced services

The advanced services in Apps Script let experienced developers connect to certain public Google APIs with less set-up than using their HTTP interfaces. Advanced services are essentially thin wrappers around those Google APIs. They work much like Apps Script's [built-in services](/apps-script/guides/services)—for example, they offer autocomplete, and Apps Script handles the [authorization flow](/apps-script/guides/services/authorization) automatically. However, you must enable an advanced service before you can use it in a script.

To see which Google APIs are available as advanced services, look for the **Advanced Google Services** section in the [Reference](/apps-script/reference). If you want to use a Google API that isn't available as an advanced service, just connect to it like any other [external API](/apps-script/guides/services/external).

### Advanced services or HTTP?

Each of the advanced Google services is associated with a public Google API. In Apps Script, you can access these APIs via advanced services or by simply making the API requests directly using [`UrlFetch`](/apps-script/reference/url-fetch).

**If you use the advanced service method**, Apps Script handles the [authorization flow](/apps-script/guides/services/authorization) and offers autocomplete support. However, you must enable the advanced service before you can use it. In addition, some advanced services only provide a subset of the functionality available in the API.

**If you use the `UrlFetch` method to access the API directly**, you are essentially treating the Google API as an [external API](/apps-script/guides/services/external). With this method, all aspects of the API can be used. However, it requires you to handle the API authorization yourself. You must also construct any needed headers and parse the API responses.

In general it's easiest to use an advanced service where possible and only use the `UrlFetch` method when the advanced service doesn't provide the functionality you need.

### Requirements

Before you can use an advanced service, you must satisfy the following requirements:

1.  You must [enable the advanced service](/apps-script/guides/services/advanced#enable_advanced_services) in your script project.
2.  You must make sure the API corresponding to the advanced service is enabled in the [Cloud Platform (GCP) project](/apps-script/guides/cloud-platform-projects) your script uses.
    
    If your script project uses a [default GCP project](/apps-script/guides/cloud-platform-projects#default_cloud_platform_projects) created on or after April 8, 2019, the API is enabled automatically after you enable the advanced service and save the script project. If you have not done so already, you may also be asked to agree to the [Google Cloud](https://cloud.google.com/terms/) and [Google APIs](/terms) Terms of Service as well.
    
    If your script project uses a [standard GCP project](/apps-script/guides/cloud-platform-projects#standard_cloud_platform_projects) or an older default GCP project, you must [enable the advanced service's corresponding API](/apps-script/guides/cloud-platform-projects#enabling_an_api_in_a_standard_gcp_project) in the GCP project manually. You must have edit access to the GCP project to make this change.
    

See [Cloud Platform projects](/apps-script/guides/cloud-platform-projects) for more information.

### Enable advanced services

To use an advanced Google service, follow these instructions:

1.  Open the Apps Script project.
2.  At the left, click **Editor** code.
3.  At the left, next to **Services**, click **Add a service** add.
4.  Select an advanced Google service and click **Add**.

**Note:** If you're using a [standard GCP project](/apps-script/guides/cloud-platform-projects#standard_cloud_platform_projects) (or an older default GCP project that was created prior to April 8, 2019), you must also manually enable the API corresponding to the advanced service. Enable the API by doing the following:

1.  Open the GCP project associated with your script in the [**Google Cloud console**](https://console.developers.google.com/apis/dashboard).
2.  At the top of the console, click into the search bar and type part of the name of the API (for example, "Calendar"), then click the name once you see it.
3.  On the next screen, click **Enable API**.
4.  Close the Google Cloud console and return to the script editor. Click **OK** in the dialog.

After you enable an advanced service, it's available in autocomplete.

### How method signatures are determined

Advanced services generally use the same objects, method names, and parameters as the corresponding public APIs, although method signatures are translated for use in Apps Script. The script editor's [autocomplete function](/apps-script/guides/services#using_autocomplete) usually provides enough information to get started, but the rules below explain how Apps Script generates a method signature from a public Google API.

Requests to Google APIs can accept a variety of different types of data, including path parameters, query parameters, a request body, and/or a media upload attachment. Some advanced services can also accept specific HTTP request headers (for example, the [Calendar advanced service](/apps-script/advanced/calendar)).

The corresponding method signature in Google Apps Script has the following arguments:

1.  The request body (usually a resource), as a JavaScript object.
2.  Path or required parameters, as individual arguments.
3.  The media upload attachment, as a [`Blob`](/apps-script/reference/base/blob) argument.
4.  Optional parameters, as a JavaScript object mapping parameter names to values.
5.  HTTP request headers, as a JavaScript object mapping header names to header values.

If the method doesn't have any items in a given category, that part of the signature is omitted.

There are some special exceptions to be aware of:

-   For methods that accept a media upload, the parameter `uploadType` is set automatically.
-   Methods named `delete` in the Google API are named `remove` in Apps Script, since `delete` is a reserved word in JavaScript.
-   If an advanced service is configured to accept HTTP request headers, and you set a request headers JavaScript object, then you must also set the optional parameters JavaScript object (to an empty object if you aren't using optional parameters).

### Support for advanced services

Advanced services are just thin wrappers that enables the use of a Google APIs within Apps Script. As such, any issue encountered while using them is usually an issue with the underlying API, not with Apps Script itself.

If you encounter a problem while using an advanced service, it should be reported using the support instructions for the underlying API. Links to these support instructions are provided in each advanced service guide in the Apps Script [Reference](/apps-script/reference) section.

---

## Google Sheets API Reference

This section provides a reference for the Google Sheets REST API.

bookmark\_borderbookmark Stay organized with collections Save and categorize content based on your preferences.

-   On this page
-   Service: sheets.googleapis.com
    -   Discovery document
    -   Service endpoint
-   REST Resource: v4.spreadsheets
-   REST Resource: v4.spreadsheets.developerMetadata
-   REST Resource: v4.spreadsheets.sheets
-   REST Resource: v4.spreadsheets.values

Reads and writes Google Sheets.

-   REST Resource: v4.spreadsheets
-   REST Resource: v4.spreadsheets.developerMetadata
-   REST Resource: v4.spreadsheets.sheets
-   REST Resource: v4.spreadsheets.values

### Service: sheets.googleapis.com

To call this service, we recommend that you use the Google-provided [client libraries](https://cloud.google.com/apis/docs/client-libraries-explained). If your application needs to use your own libraries to call this service, use the following information when you make the API requests.

#### Discovery document

A [Discovery Document](https://developers.google.com/discovery/v1/reference/apis) is a machine-readable specification for describing and consuming REST APIs. It is used to build client libraries, IDE plugins, and other tools that interact with Google APIs. One service may provide multiple discovery documents. This service provides the following discovery document:

-   [https://sheets.googleapis.com/$discovery/rest?version=v4](https://sheets.googleapis.com/$discovery/rest?version=v4)

#### Service endpoint

A [service endpoint](https://cloud.google.com/apis/design/glossary#api_service_endpoint) is a base URL that specifies the network address of an API service. One service might have multiple service endpoints. This service has the following service endpoint and all URIs below are relative to this service endpoint:

-   `https://sheets.googleapis.com`

### REST Resource: [v4.spreadsheets](/workspace/sheets/api/reference/rest/v4/spreadsheets)

| Methods |
| --- |
| `[batchUpdate](/workspace/sheets/api/reference/rest/v4/spreadsheets/batchUpdate)` | `POST /v4/spreadsheets/{spreadsheetId}:batchUpdate` Applies one or more updates to the spreadsheet. |
| `[create](/workspace/sheets/api/reference/rest/v4/spreadsheets/create)` | `POST /v4/spreadsheets` Creates a spreadsheet, returning the newly created spreadsheet. |
| `[get](/workspace/sheets/api/reference/rest/v4/spreadsheets/get)` | `GET /v4/spreadsheets/{spreadsheetId}` Returns the spreadsheet at the given ID. |
| `[getByDataFilter](/workspace/sheets/api/reference/rest/v4/spreadsheets/getByDataFilter)` | `POST /v4/spreadsheets/{spreadsheetId}:getByDataFilter` Returns the spreadsheet at the given ID. |

### REST Resource: [v4.spreadsheets.developerMetadata](/workspace/sheets/api/reference/rest/v4/spreadsheets.developerMetadata)

| Methods |
| --- |
| `[get](/workspace/sheets/api/reference/rest/v4/spreadsheets.developerMetadata/get)` | `GET /v4/spreadsheets/{spreadsheetId}/developerMetadata/{metadataId}` Returns the developer metadata with the specified ID. |
| `[search](/workspace/sheets/api/reference/rest/v4/spreadsheets.developerMetadata/search)` | `POST /v4/spreadsheets/{spreadsheetId}/developerMetadata:search` Returns all developer metadata matching the specified `[DataFilter](/workspace/sheets/api/reference/rest/v4/DataFilter)`. |

### REST Resource: [v4.spreadsheets.sheets](/workspace/sheets/api/reference/rest/v4/spreadsheets.sheets)

| Methods |
| --- |
| `[copyTo](/workspace/sheets/api/reference/rest/v4/spreadsheets.sheets/copyTo)` | `POST /v4/spreadsheets/{spreadsheetId}/sheets/{sheetId}:copyTo` Copies a single sheet from a spreadsheet to another spreadsheet. |

### REST Resource: [v4.spreadsheets.values](/workspace/sheets/api/reference/rest/v4/spreadsheets.values)

| Methods |
| --- |
| `[append](/workspace/sheets/api/reference/rest/v4/spreadsheets.values/append)` | `POST /v4/spreadsheets/{spreadsheetId}/values/{range}:append` Appends values to a spreadsheet. |
| `[batchClear](/workspace/sheets/api/reference/rest/v4/spreadsheets.values/batchClear)` | `POST /v4/spreadsheets/{spreadsheetId}/values:batchClear` Clears one or more ranges of values from a spreadsheet. |
| `[batchClearByDataFilter](/workspace/sheets/api/reference/rest/v4/spreadsheets.values/batchClearByDataFilter)` | `POST /v4/spreadsheets/{spreadsheetId}/values:batchClearByDataFilter` Clears one or more ranges of values from a spreadsheet. |
| `[batchGet](/workspace/sheets/api/reference/rest/v4/spreadsheets.values/batchGet)` | `GET /v4/spreadsheets/{spreadsheetId}/values:batchGet` Returns one or more ranges of values from a spreadsheet. |
| `[batchGetByDataFilter](/workspace/sheets/api/reference/rest/v4/spreadsheets.values/batchGetByDataFilter)` | `POST /v4/spreadsheets/{spreadsheetId}/values:batchGetByDataFilter` Returns one or more ranges of values that match the specified data filters. |
| `[batchUpdate](/workspace/sheets/api/reference/rest/v4/spreadsheets.values/batchUpdate)` | `POST /v4/spreadsheets/{spreadsheetId}/values:batchUpdate` Sets values in one or more ranges of a spreadsheet. |
| `[batchUpdateByDataFilter](/workspace/sheets/api/reference/rest/v4/spreadsheets.values/batchUpdateByDataFilter)` | `POST /v4/spreadsheets/{spreadsheetId}/values:batchUpdateByDataFilter` Sets values in one or more ranges of a spreadsheet. |
| `[clear](/workspace/sheets/api/reference/rest/v4/spreadsheets.values/clear)` | `POST /v4/spreadsheets/{spreadsheetId}/values/{range}:clear` Clears values from a spreadsheet. |
| `[get](/workspace/sheets/api/reference/rest/v4/spreadsheets.values/get)` | `GET /v4/spreadsheets/{spreadsheetId}/values/{range}` Returns a range of values from a spreadsheet. |
| `[update](/workspace/sheets/api/reference/rest/v4/spreadsheets.values/update)` | `PUT /v4/spreadsheets/{spreadsheetId}/values/{range}` Sets values in a range of a spreadsheet. |
