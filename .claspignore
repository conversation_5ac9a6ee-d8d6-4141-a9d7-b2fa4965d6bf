# Clasp ignore file for Google Apps Script deployment
# This file specifies which files and directories should NOT be uploaded to Apps Script

# Local version directory (browser-based version)
local/
local/**

# Documentation files (not needed in Apps Script)
README.md
DEPLOYMENT.md
apps_script_api_reference/

# Example files
example_event_ids.csv

# Shared utilities (included directly in Code.js for Apps Script)
shared-utils.js

# CSS file for local version (Apps Script uses style.html instead)
style.css

# JavaScript file for local version (Apps Script uses script.html instead)
script.js

# Test files
test-refactoring.js
REFACTORING_SUMMARY.md

# Version control and development files
.git/
.gitignore
.claspignore

# Node.js files (if any)
node_modules/
package.json
package-lock.json

# Python files
*.py
__pycache__/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
