/**
 * Shared Utilities for Google Meet Attendance Export
 * Used by both Apps Script and Local versions
 */

/**
 * Parse CSV data to extract event IDs
 * @param {string} csvData - CSV string
 * @return {Array} - Array of event IDs
 */
function parseCsvEventIds(csvData) {
  console.log('📊 Parsing CSV data:', csvData);

  if (!csvData || typeof csvData !== 'string') {
    throw new Error('Invalid CSV data provided');
  }

  // Split by lines and filter out empty lines
  const lines = csvData.split(/\r?\n/).filter(line => line.trim());
  const eventIds = [];

  for (const line of lines) {
    // Split by comma and take the first column (assuming event ID is in first column)
    const columns = line.split(',');
    const eventId = columns[0].trim();

    if (eventId && eventId.length > 0) {
      eventIds.push(eventId);
    }
  }

  console.log(`✅ Parsed ${eventIds.length} event IDs`);
  return eventIds;
}

/**
 * Calculate session duration in minutes
 * @param {string} startTime - ISO timestamp
 * @param {string} endTime - ISO timestamp
 * @return {number} - Duration in minutes
 */
function calculateSessionDuration(startTime, endTime) {
  if (!startTime || !endTime) return 0;

  const start = new Date(startTime);
  const end = new Date(endTime);

  return Math.round((end - start) / (1000 * 60)); // Convert to minutes
}

/**
 * Calculate total duration across all sessions
 * @param {Array} sessions - Array of session objects
 * @return {number} - Total duration in minutes
 */
function calculateTotalDuration(sessions) {
  return sessions.reduce((total, session) => total + session.duration, 0);
}

/**
 * Generate summary statistics
 * @param {Array} attendanceData - Array of event attendance data
 * @return {Object} - Summary statistics
 */
function generateSummary(attendanceData) {
  console.log('📈 Generating summary for attendance data');

  const summary = {
    totalEvents: attendanceData.length,
    totalParticipants: 0,
    averageParticipantsPerEvent: 0,
    totalAttendanceMinutes: 0,
    averageAttendancePerParticipant: 0,
    uniqueParticipants: new Set(),
    uniqueParticipantCount: 0
  };

  let totalDuration = 0;
  let totalParticipantCount = 0;

  for (const event of attendanceData) {
    totalParticipantCount += event.participants.length;

    for (const participant of event.participants) {
      if (participant.email) {
        summary.uniqueParticipants.add(participant.email);
      }
      totalDuration += participant.totalDuration;
    }
  }

  summary.totalParticipants = totalParticipantCount;
  summary.averageParticipantsPerEvent = attendanceData.length > 0 ?
    Math.round(totalParticipantCount / attendanceData.length * 100) / 100 : 0;
  summary.totalAttendanceMinutes = totalDuration;
  summary.averageAttendancePerParticipant = totalParticipantCount > 0 ?
    Math.round(totalDuration / totalParticipantCount * 100) / 100 : 0;
  summary.uniqueParticipantCount = summary.uniqueParticipants.size;

  // Convert Set to Array for JSON serialization
  summary.uniqueParticipants = Array.from(summary.uniqueParticipants);

  console.log('✅ Summary generated:', summary);
  return summary;
}

/**
 * Enhanced error handling with environment-specific logging
 * @param {string} context - Context where error occurred
 * @param {Error} error - The error object
 * @param {Object} additionalInfo - Additional information to log
 */
function logError(context, error, additionalInfo = {}) {
  const errorInfo = {
    context: context,
    message: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString(),
    ...additionalInfo
  };

  console.error(`❌ Error in ${context}:`, errorInfo);
  
  // In Apps Script, also log to Stackdriver
  if (typeof console.error === 'function') {
    console.error(JSON.stringify(errorInfo, null, 2));
  }
}

/**
 * Enhanced logging with environment detection
 * @param {string} level - Log level (info, warn, error)
 * @param {string} message - Log message
 * @param {Object} data - Additional data to log
 */
function enhancedLog(level, message, data = {}) {
  const logEntry = {
    level: level,
    message: message,
    timestamp: new Date().toISOString(),
    data: data
  };

  switch (level) {
    case 'error':
      console.error(`❌ ${message}`, data);
      break;
    case 'warn':
      console.warn(`⚠️ ${message}`, data);
      break;
    case 'info':
    default:
      console.log(`ℹ️ ${message}`, data);
      break;
  }
}

/**
 * Validate event ID format
 * @param {string} eventId - Event ID to validate
 * @return {boolean} - Whether the event ID appears valid
 */
function isValidEventId(eventId) {
  if (!eventId || typeof eventId !== 'string') {
    return false;
  }

  // Basic validation - Google Calendar event IDs are typically alphanumeric
  // and contain underscores, but this is a loose validation
  const trimmed = eventId.trim();
  return trimmed.length > 0 && !/[<>"]/.test(trimmed);
}

/**
 * Rate limiting utility for API calls
 * @param {number} delayMs - Delay in milliseconds
 * @return {Promise} - Promise that resolves after delay
 */
function rateLimitDelay(delayMs = 1000) {
  return new Promise(resolve => {
    if (typeof Utilities !== 'undefined' && Utilities.sleep) {
      // Apps Script environment
      Utilities.sleep(delayMs);
      resolve();
    } else {
      // Browser environment
      setTimeout(resolve, delayMs);
    }
  });
}

/**
 * Format date/time for display
 * @param {string} dateString - ISO date string
 * @return {string} - Formatted date/time
 */
function formatDateTime(dateString) {
  try {
    return new Date(dateString).toLocaleString();
  } catch (error) {
    console.warn('⚠️ Failed to format date:', dateString);
    return dateString;
  }
}

/**
 * Format time for display
 * @param {string} dateString - ISO date string
 * @return {string} - Formatted time
 */
function formatTime(dateString) {
  try {
    return new Date(dateString).toLocaleTimeString();
  } catch (error) {
    console.warn('⚠️ Failed to format time:', dateString);
    return dateString;
  }
}

/**
 * Escape HTML for safe display
 * @param {string} text - Text to escape
 * @return {string} - HTML-escaped text
 */
function escapeHtml(text) {
  if (!text) return '';
  
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

// Export functions for different environments
if (typeof module !== 'undefined' && module.exports) {
  // Node.js environment
  module.exports = {
    parseCsvEventIds,
    calculateSessionDuration,
    calculateTotalDuration,
    generateSummary,
    logError,
    enhancedLog,
    isValidEventId,
    rateLimitDelay,
    formatDateTime,
    formatTime,
    escapeHtml
  };
} else if (typeof window !== 'undefined') {
  // Browser environment - attach to window
  window.SharedUtils = {
    parseCsvEventIds,
    calculateSessionDuration,
    calculateTotalDuration,
    generateSummary,
    logError,
    enhancedLog,
    isValidEventId,
    rateLimitDelay,
    formatDateTime,
    formatTime,
    escapeHtml
  };
}
