console.log('--- TEST_SCRIPT.HTML LOADED ---');
alert('Alert from test_script.html');

document.addEventListener('DOMContentLoaded', function() {
    console.log('--- DOMContentLoaded from test_script.html ---');
    const btn = document.getElementById('submitBtn');
    if (btn) {
        console.log('--- Submit button found by test_script.html ---');
        btn.addEventListener('click', function() {
            alert('Submit button clicked (handler from test_script.html)!');
        });
    } else {
        console.log('--- Submit button NOT found by test_script.html ---');
    }
});
