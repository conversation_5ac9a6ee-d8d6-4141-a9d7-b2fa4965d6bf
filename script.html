// Global variables
// Version: 1.0.3 - Final client-side cleanup (no client-side validation)
let currentData = [];
let sortColumn = '';
let sortDirection = 'asc';

// DOM elements - only access in browser environment
let eventForm, csvInput, submitBtn, clearBtn, progressSection, progressFill, progressText;
let errorSection, errorList, summarySection, summaryGrid, resultsSection, resultsTable;
let resultsTableBody, searchInput, exportBtn; // Removed csvInputError from here

// Initialize DOM elements when document is ready
function initializeDOMElements() {
    eventForm = document.getElementById('eventForm');
    csvInput = document.getElementById('csvInput');
    // csvInputError = document.getElementById('csvInputError'); // Removed
    submitBtn = document.getElementById('submitBtn');
    clearBtn = document.getElementById('clearBtn');
    progressSection = document.getElementById('progressSection');
    progressFill = document.getElementById('progressFill');
    progressText = document.getElementById('progressText');
    errorSection = document.getElementById('errorSection');
    errorList = document.getElementById('errorList');
    summarySection = document.getElementById('summarySection');
    summaryGrid = document.getElementById('summaryGrid');
    resultsSection = document.getElementById('resultsSection');
    resultsTable = document.getElementById('resultsTable');
    resultsTableBody = document.getElementById('resultsTableBody');
    searchInput = document.getElementById('searchInput');
    exportBtn = document.getElementById('exportBtn');
}

// Debug function to check environment
function debugEnvironment() {
    console.log('🔍 Environment Debug Info:');
    console.log('- Document available:', typeof document !== 'undefined');
    console.log('- Google Apps Script available:', typeof google !== 'undefined' && !!google.script);
    console.log('- DOM elements found:', {
        eventForm: !!eventForm,
        csvInput: !!csvInput,
        // csvInputError: !!csvInputError, // Removed
        submitBtn: !!submitBtn,
        clearBtn: !!clearBtn,
        progressSection: !!progressSection,
        errorSection: !!errorSection,
        summarySection: !!summarySection,
        resultsSection: !!resultsSection
    });
    console.log('- Current data length:', currentData.length);
}

// Make debug function globally available
window.debugEnvironment = debugEnvironment;

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Apps Script frontend initializing...');

    try {
        // Initialize DOM elements
        initializeDOMElements();

        // Debug environment
        debugEnvironment();

        // Add event listeners with error handling
        if (submitBtn) {
            submitBtn.addEventListener('click', handleFormSubmit);
            console.log('✅ Submit button click listener added');
        } else {
            console.error('❌ Submit button not found');
        }

        // Removed client-side validation listener
        // if (csvInput) {
        //     csvInput.addEventListener('input', () => validateCsvInput(false));
        //     console.log('✅ CSV input listener added');
        // }


        if (clearBtn) {
            clearBtn.addEventListener('click', handleClear);
            console.log('✅ Clear button listener added');
        }

        if (searchInput) {
            searchInput.addEventListener('input', handleSearch);
            console.log('✅ Search input listener added');
        }

        if (exportBtn) {
            exportBtn.addEventListener('click', handleExport);
            console.log('✅ Export button listener added');
        }

        // Add sort listeners to table headers
        const sortableHeaders = document.querySelectorAll('.sortable');
        sortableHeaders.forEach(header => {
            header.addEventListener('click', () => handleSort(header.dataset.sort));
        });
        console.log(`✅ Sort listeners added to ${sortableHeaders.length} headers`);

        console.log('✅ Apps Script frontend initialized successfully');
    } catch (initError) {
        console.error('❌ Error during initialization (CHUNK 2):', initError);
        displayError('Initialization error (CHUNK 2): ' + initError.message);
    }
});

/**
 * Handle form submission
 */
async function handleFormSubmit() {
    console.log('📝 Submit button clicked');

    // Validate DOM elements exist
    if (!csvInput) {
        displayError('Critical UI error: CSV input element not found.');
        return;
    }

    const csvData = csvInput.value.trim();
    // No client-side validation here, relying on server-side
    // if (!validateCsvInput(true)) { return; } // Removed

    console.log('📊 CSV data to process:', csvData);

    // Reset UI
    hideAllSections();
    setLoadingState(true);
    showProgressSection();

    // Check if google.script.run is available
    if (typeof google === 'undefined' || !google.script || !google.script.run) {
        displayError('Google Apps Script runtime not available. This may be a development environment.');
        setLoadingState(false);
        hideProgressSection();
        return;
    }

    try {
        // Call the backend function
        console.log('🔄 Calling processEventIds...');
        const result = await new Promise((resolve, reject) => {
            const runner = google.script.run
                .withSuccessHandler((response) => {
                    console.log('✅ Success handler called:', response);
                    resolve(response);
                })
                .withFailureHandler((error) => {
                    console.error('❌ Failure handler called:', error);
                    reject(new Error(error.message || error.toString()));
                });

            // Add timeout to prevent hanging
            setTimeout(() => {
                reject(new Error('Request timeout - no response from server after 60 seconds'));
            }, 60000);

            runner.processEventIds(csvData);
        });

        console.log('✅ Backend response received:', result);

        if (result && result.success) {
            displayResults(result);
        } else {
            // Check for server-side validation errors
            if (result.validationErrors && Array.isArray(result.validationErrors) && result.validationErrors.length > 0) {
                displayErrors(result.validationErrors.map(err => ({ eventId: 'N/A', error: err }))); // Format for displayErrors
            } else {
                const errorMsg = result ? result.error : 'Unknown error - no response data';
                displayError('Processing failed: ' + errorMsg);
            }
        }

    } catch (backendError) {
        console.error('❌ Backend call error:', backendError);
        displayError('Failed to process events: ' + backendError.message);
    } finally {
        setLoadingState(false);
        hideProgressSection();
    }
}

/**
 * Handle clear button
 */
function handleClear() {
    console.log('🧹 Clearing form and results');
    csvInput.value = '';
    // Removed csvInputError related DOM manipulation
    // if (csvInputError) csvInputError.textContent = '';
    // if (csvInput) csvInput.classList.remove('border-red-500', 'focus:ring-red-300');
    // if (csvInput) csvInput.classList.add('border-blue-400', 'focus:ring-blue-300');
    hideAllSections();
    currentData = [];
}

    /**
     * Handle search input
     */
    function handleSearch() {
        const searchTerm = searchInput.value.toLowerCase();
        console.log('🔍 Searching for:', searchTerm);

        const rows = resultsTableBody.querySelectorAll('tr');
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    }

    /**
     * Handle table sorting
     */
    function handleSort(column) {
        console.log('📊 Sorting by column:', column);

        if (sortColumn === column) {
            sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            sortColumn = column;
            sortDirection = 'asc';
        }

        // Update header classes and add/update sort indicators
        const headers = document.querySelectorAll('.sortable');
        headers.forEach(header => {
            let sortIcon = header.querySelector('.sort-icon');
            if (!sortIcon) {
                sortIcon = document.createElement('span');
                sortIcon.classList.add('sort-icon', 'ml-1', 'inline-block', 'transition-transform', 'duration-200');
                header.appendChild(sortIcon);
            }

            // Reset all icons
            sortIcon.innerHTML = ''; // Clear existing icon
            sortIcon.classList.remove('rotate-180', 'text-blue-600'); // Reset rotation and color

            if (header.dataset.sort === column) {
                sortIcon.innerHTML = '&#9650;'; // Up arrow
                sortIcon.classList.add('text-blue-600'); // Highlight active sort column
                if (sortDirection === 'desc') {
                    sortIcon.classList.add('rotate-180'); // Rotate for descending
                }
            }
        });

        // Sort the data
        const sortedData = [...currentData].sort((a, b) => {
            let aVal, bVal;

            switch (column) {
                case 'eventTitle':
                    aVal = a.eventTitle;
                    bVal = b.eventTitle;
                    break;
                case 'eventStart':
                    aVal = new Date(a.eventStart);
                    bVal = new Date(b.eventStart);
                    break;
                case 'participantName':
                    aVal = a.participantName;
                    bVal = b.participantName;
                    break;
                case 'participantEmail':
                    aVal = a.participantEmail || '';
                    bVal = b.participantEmail || '';
                    break;
                case 'totalDuration':
                    aVal = a.totalDuration;
                    bVal = b.totalDuration;
                    break;
                case 'sessionCount':
                    aVal = a.sessionCount;
                    bVal = b.sessionCount;
                    break;
                default:
                    return 0;
            }

            let diff = 0;
            if (typeof aVal === 'string' && typeof bVal === 'string') {
                diff = aVal.localeCompare(bVal);
            } else if (aVal instanceof Date && bVal instanceof Date) {
                diff = aVal.getTime() - bVal.getTime();
            } else { // Assume numbers
                diff = aVal - bVal;
            }

            // Normalize diff to -1, 0, or 1 without using < or > in the final return path
            let resultSign = 0;
            if (diff !== 0) {
                resultSign = Math.sign(diff); // Returns -1, 0, or 1 (or NaN if diff is NaN)
            }
            
            // Ensure resultSign is not NaN if diff was NaN (e.g. comparing undefined values)
            if (isNaN(resultSign)) {
                resultSign = 0;
            }

            // Use explicit comparison to avoid < and >
            if (sortDirection === 'asc') {
                return resultSign;
            } else {
                return -resultSign;
            }
        });

        renderTable(sortedData);
    }

/**
 * Handle CSV export
 */
function handleExport() {
    console.log('📤 Exporting data to CSV');

    if (currentData.length === 0) {
        displayError('No data to export');
        return;
    }

    const headers = ['Event Title', 'Event Date', 'Participant Name', 'Email', 'Duration (min)', 'Sessions', 'Join/Leave Times'];
    const csvContent = [
        headers.join(','),
        ...currentData.map(row => [
            `"${row.eventTitle}"`,
            `"${formatDateTime(row.eventStart)}"`,
            `"${row.participantName}"`,
            `"${row.participantEmail || ''}"`,
            row.totalDuration,
            row.sessionCount,
            `"${row.sessionTimes}"`
        ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `meet_attendance_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    console.log('✅ CSV export completed');
}

/**
 * Display results
 */
function displayResults(result) {
    try {
        console.log('📊 Displaying results:', result);

        if (!result) {
            throw new Error('No result data received');
        }

        // Show errors if any
        if (result.errors && Array.isArray(result.errors) && result.errors.length > 0) {
            try {
                displayErrors(result.errors);
            } catch (errorDisplayError) {
                console.error('❌ Error displaying errors:', errorDisplayError);
            }
        }

        // Show summary
        if (result.summary) {
            try {
                displaySummary(result.summary);
            } catch (summaryError) {
                console.error('❌ Error displaying summary:', summaryError);
            }
        }

        // Process and display attendance data
        if (result.data && Array.isArray(result.data) && result.data.length > 0) {
            try {
                currentData = processDataForTable(result.data);
                renderTable(currentData);
                showResultsSection();
            } catch (dataError) {
                console.error('❌ Error processing/displaying data:', dataError);
                displayError('Error displaying results: ' + dataError.message);
            }
        } else {
            displayError('No attendance data found for the provided event IDs');
        }
    } catch (error) {
        console.error('❌ Critical error in displayResults:', error);
        try {
            displayError('Error displaying results: ' + error.message);
        } catch (fallbackError) {
            console.error('❌ Could not display error message:', fallbackError);
            displayError('Critical error displaying results: ' + error.message);
        }
    }
}

/**
 * Process data for table display
 */
function processDataForTable(data) {
    console.log('🔄 Processing data for table display');

    const tableData = [];

    data.forEach(event => {
        event.participants.forEach(participant => {
            tableData.push({
                eventTitle: event.eventTitle,
                eventStart: event.eventStart,
                participantName: participant.name,
                participantEmail: participant.email,
                totalDuration: participant.totalDuration,
                sessionCount: participant.sessions.length,
                sessionTimes: participant.sessions.map(session =>
                    `${formatTime(session.startTime)} - ${formatTime(session.endTime)}`
                ).join('; ')
            });
        });
    });

    console.log(`✅ Processed ${tableData.length} participant records`);
    return tableData;
}

/**
 * Render table with data
 */
function renderTable(data) {
    console.log('📋 Rendering table with', data.length, 'rows');

    resultsTableBody.innerHTML = '';

    data.forEach(row => {
        const tr = document.createElement('tr');
        tr.classList.add('hover:bg-gray-50'); // Add hover effect for rows
        tr.innerHTML = `
            <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">${escapeHtml(row.eventTitle)}</td>
            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-600">${formatDateTime(row.eventStart)}</td>
            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">${escapeHtml(row.participantName)}</td>
            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-600">${escapeHtml(row.participantEmail || 'N/A')}</td>
            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-600">${row.totalDuration}</td>
            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-600">${row.sessionCount}</td>
            <td class="px-4 py-2 text-sm text-gray-600">${escapeHtml(row.sessionTimes)}</td>
        `;
        resultsTableBody.appendChild(tr);
    });
}

/**
 * Display summary statistics
 */
function displaySummary(summary) {
    console.log('📈 Displaying summary:', summary);

    summaryGrid.innerHTML = `
        <div class="bg-gray-50 p-3 rounded-lg text-center border-l-4 border-blue-600 shadow-sm">
            <span class="block text-2xl font-bold text-blue-700">${summary.totalEvents}</span>
            <span class="block text-xs text-gray-600 mt-1">Total Events</span>
        </div>
        <div class="bg-gray-50 p-3 rounded-lg text-center border-l-4 border-blue-600 shadow-sm">
            <span class="block text-2xl font-bold text-blue-700">${summary.totalParticipants}</span>
            <span class="block text-xs text-gray-600 mt-1">Total Participants</span>
        </div>
        <div class="bg-gray-50 p-3 rounded-lg text-center border-l-4 border-blue-600 shadow-sm">
            <span class="block text-2xl font-bold text-blue-700">${summary.uniqueParticipantCount}</span>
            <span class="block text-xs text-gray-600 mt-1">Unique Participants</span>
        </div>
        <div class="bg-gray-50 p-3 rounded-lg text-center border-l-4 border-blue-600 shadow-sm">
            <span class="block text-2xl font-bold text-blue-700">${summary.averageParticipantsPerEvent}</span>
            <span class="block text-xs text-gray-600 mt-1">Avg Participants/Event</span>
        </div>
        <div class="bg-gray-50 p-3 rounded-lg text-center border-l-4 border-blue-600 shadow-sm">
            <span class="block text-2xl font-bold text-blue-700">${summary.totalAttendanceMinutes}</span>
            <span class="block text-xs text-gray-600 mt-1">Total Minutes</span>
        </div>
        <div class="bg-gray-50 p-3 rounded-lg text-center border-l-4 border-blue-600 shadow-sm">
            <span class="block text-2xl font-bold text-blue-700">${summary.averageAttendancePerParticipant}</span>
            <span class="block text-xs text-gray-600 mt-1">Avg Minutes/Participant</span>
        </div>
    `;

    showSummarySection();
}

/**
 * Display errors
 */
function displayErrors(errors) {
    console.log('⚠️ Displaying errors:', errors);

    errorList.innerHTML = errors.map(error =>
        `<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg relative mb-2 last:mb-0 shadow-sm" role="alert">
            <strong class="font-bold">Event ID:</strong> ${escapeHtml(error.eventId)}<br>
            <strong class="font-bold">Error:</strong> ${escapeHtml(error.error)}
        </div>`
    ).join('');

    showErrorSection();
}

/**
 * Display single error
 */
function displayError(message) {
    console.error('❌ Displaying error:', message);

    errorList.innerHTML = `<div class="bg-red-100 border border-red-400 text-red-700 px-5 py-4 rounded-lg relative shadow-sm" role="alert">${escapeHtml(message)}</div>`;
    showErrorSection();
}

// Utility functions with error handling
function setLoadingState(loading) {
    try {
        if (!submitBtn) {
            console.warn('⚠️ Submit button not found');
            return;
        }

        submitBtn.disabled = loading;
        const btnText = submitBtn.querySelector('.btn-text');
        const spinner = submitBtn.querySelector('.loading-spinner');

        if (btnText) {
            btnText.textContent = loading ? 'Processing...' : 'Process Events';
        }

        if (spinner) {
            spinner.style.display = loading ? 'inline-block' : 'none';
        }
    } catch (error) {
        console.error('❌ Error in setLoadingState:', error);
    }
}

function hideAllSections() {
    try {
        const sections = [progressSection, errorSection, summarySection, resultsSection];
        sections.forEach(section => {
            if (section) {
                section.style.display = 'none';
            }
        });
    } catch (error) {
        console.error('❌ Error in hideAllSections:', error);
    }
}

function showProgressSection() {
    try {
        if (progressSection) {
            progressSection.style.display = 'block';
        }
    } catch (error) {
        console.error('❌ Error in showProgressSection:', error);
    }
}

function hideProgressSection() {
    try {
        if (progressSection) {
            progressSection.style.display = 'none';
        }
    } catch (error) {
        console.error('❌ Error in hideProgressSection:', error);
    }
}

function showErrorSection() {
    try {
        if (errorSection) {
            errorSection.style.display = 'block';
        }
    } catch (error) {
        console.error('❌ Error in showErrorSection:', error);
    }
}

function showSummarySection() {
    try {
        if (summarySection) {
            summarySection.style.display = 'block';
        }
    } catch (error) {
        console.error('❌ Error in showSummarySection:', error);
    }
}

function showResultsSection() {
    try {
        if (resultsSection) {
            resultsSection.style.display = 'block';
        }
    } catch (error) {
        console.error('❌ Error in showResultsSection:', error);
    }
}

function formatDateTime(dateString) {
    try {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleString();
    }
    catch (error) {
        console.error('❌ Error formatting date:', error);
        return dateString || 'N/A';
    }
}

function formatTime(dateString) {
    try {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleTimeString();
    }
    catch (error) {
        console.error('❌ Error formatting time:', error);
        return dateString || 'N/A';
    }
}

function escapeHtml(text) {
    try {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    catch (error) {
        console.error('❌ Error escaping HTML:', error);
        return String(text || '');
    }
}

// Global error handlers to prevent page from going blank
window.addEventListener('error', function(event) {
    console.error('🚨 Global JavaScript error:', event.error);
    console.error('Error details:', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack
    });

    // Try to display error to user
    try {
        if (typeof displayError === 'function') {
            displayError('JavaScript Error: ' + event.message);
        } else {
            displayError('JavaScript Error: ' + event.message);
        }
    } catch (e) {
        console.error('❌ Could not display error to user:', e);
    }

    // Prevent default error handling that might cause blank page
    event.preventDefault();
    return false;
});

window.addEventListener('unhandledrejection', function(event) {
    console.error('🚨 Unhandled Promise rejection:', event.reason);

    // Try to display error to user
    try {
        if (typeof displayError === 'function') {
            displayError('Promise Error: ' + (event.reason?.message || event.reason));
        } else {
            displayError('Promise Error: ' + (event.reason?.message || event.reason));
        }
    } catch (e) {
        console.error('❌ Could not display promise error to user:', e);
    }

    // Prevent default handling
    event.preventDefault();
});

console.log('✅ Apps Script frontend script loaded successfully');
console.log('🛡️ Global error handlers installed');
