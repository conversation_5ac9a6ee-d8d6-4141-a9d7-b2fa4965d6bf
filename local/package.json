{"name": "google-meet-attendance-export-local", "version": "1.0.0", "description": "Local version of Google Meet Attendance Export application", "main": "index.html", "scripts": {"start": "http-server -p 8000 -c-1 --cors", "dev": "http-server -p 8000 -c-1 --cors -o", "install-server": "npm install -g http-server"}, "keywords": ["google-meet", "attendance", "export", "calendar", "local"], "author": "Google Meet Attendance Export", "license": "MIT", "devDependencies": {"http-server": "^14.1.1"}, "repository": {"type": "git", "url": "."}, "homepage": ".", "engines": {"node": ">=12.0.0"}}