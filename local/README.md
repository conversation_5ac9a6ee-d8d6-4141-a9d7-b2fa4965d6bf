# Google Meet Attendance Export - Local Version

A browser-based version of the Google Meet Attendance Export application that runs locally without requiring Google Apps Script.

## Quick Start

### Option 1: Python (Recommended)
```bash
cd local
python server.py
# Opens http://localhost:8000 automatically
```

### Option 2: Node.js
```bash
cd local
npm install -g http-server
npm start
# Opens http://localhost:8000
```

### Option 3: Manual Setup
1. Set up any local web server on port 8000
2. Configure Google API credentials (see setup guide)
3. Open http://localhost:8000

## Features

✅ **Same UI as Apps Script version** - Identical interface and functionality  
✅ **Demo Mode** - Test the application without real data  
✅ **Google OAuth Integration** - Secure authentication with Google  
✅ **Calendar API Access** - Read Google Calendar events  
✅ **CSV Export** - Export results to CSV format  
✅ **Responsive Design** - Works on desktop and mobile  
✅ **Verbose Logging** - Detailed console output for debugging  

## File Structure

```
local/
├── index.html          # Main application interface
├── local-script.js     # JavaScript for local version
├── config.js          # Configuration file (optional)
├── server.py          # Python development server
├── package.json       # Node.js configuration
├── LOCAL_SETUP.md     # Detailed setup instructions
└── README.md          # This file
```

## Setup Requirements

1. **Google Cloud Project** with Calendar API enabled
2. **OAuth 2.0 Credentials** configured for web application
3. **Local Web Server** (Python, Node.js, or other)

See [LOCAL_SETUP.md](LOCAL_SETUP.md) for detailed setup instructions.

## Configuration

### Method 1: Edit local-script.js directly
```javascript
const CLIENT_ID = 'your-client-id.apps.googleusercontent.com';
const API_KEY = 'your-api-key';
```

### Method 2: Use config.js (recommended)
```javascript
const CONFIG = {
    CLIENT_ID: 'your-client-id.apps.googleusercontent.com',
    API_KEY: 'your-api-key',
    // ... other settings
};
```

## Usage

### 1. Authentication
- Click "Sign In with Google"
- Grant calendar permissions
- Wait for "Authenticated" status

### 2. Demo Mode (No Authentication Required)
- Click "Load Demo Data"
- Click "Process Events"
- Explore the interface with sample data

### 3. Real Data Processing
- Enter Google Calendar event IDs (one per line or comma-separated)
- Click "Process Events"
- View results and export if needed

## Differences from Apps Script Version

| Feature | Local Version | Apps Script Version |
|---------|---------------|-------------------|
| **Setup** | Complex (OAuth) | Simple (built-in auth) |
| **Meet API** | Limited browser support | Full server-side access |
| **Hosting** | Self-hosted | Google-hosted |
| **Customization** | Full control | Limited by Apps Script |
| **Authentication** | OAuth 2.0 flow | Automatic |
| **Rate Limits** | Your project quotas | Apps Script quotas |

## Limitations

1. **Meet API Access**: Google Meet API has limited browser support
2. **OAuth Setup**: Requires Google Cloud Console configuration
3. **CORS Requirements**: Must run from web server, not file://
4. **Rate Limits**: Subject to your Google Cloud project quotas

## Troubleshooting

### Common Issues

**"Please configure your Google API credentials"**
- Update CLIENT_ID and API_KEY in local-script.js or config.js

**"Failed to initialize Google API"**
- Check that APIs are enabled in Google Cloud Console
- Verify credentials are correct

**Authentication fails**
- Ensure authorized JavaScript origins include your local server URL
- Clear browser cache and try again

**CORS errors**
- Use a web server instead of opening the file directly
- Try the included server.py or package.json scripts

### Debug Mode

1. Open browser Developer Tools (F12)
2. Check Console tab for detailed error messages
3. Monitor Network tab for API call failures

## Development

### Running the Development Server

**Python:**
```bash
python server.py [port]
```

**Node.js:**
```bash
npm run dev  # Starts server and opens browser
```

### Making Changes

1. Edit files in the `local/` directory
2. Refresh browser to see changes
3. Check browser console for any errors

### Shared Code

The local version reuses CSS from the parent directory:
- `../style.css` - Shared styling
- Utility functions are duplicated to avoid dependencies

## Security Notes

1. **API Keys**: Never commit real API keys to version control
2. **OAuth Origins**: Only add necessary authorized JavaScript origins
3. **HTTPS**: Use HTTPS in production environments
4. **Rate Limiting**: Monitor API usage in Google Cloud Console

## Production Deployment

For production use:

1. **Domain Configuration**: Update OAuth settings for your domain
2. **HTTPS**: Ensure secure connections
3. **Environment Variables**: Use secure credential storage
4. **Monitoring**: Set up logging and error tracking
5. **CDN**: Consider using a CDN for static assets

## Support

1. **Setup Issues**: See [LOCAL_SETUP.md](LOCAL_SETUP.md)
2. **API Problems**: Check Google Cloud Console logs
3. **Browser Issues**: Test in different browsers
4. **Demo Mode**: Use demo mode to test interface without API setup

## License

This project is part of the Google Meet Attendance Export application suite.
