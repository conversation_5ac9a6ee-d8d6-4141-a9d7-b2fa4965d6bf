/**
 * Configuration file for the local version of Google Meet Attendance Export
 *
 * To use this application locally, you need to:
 * 1. Create a Google Cloud Project
 * 2. Enable the required APIs
 * 3. Create OAuth 2.0 credentials
 * 4. Update the values below
 */

// Google API Configuration
const CONFIG = {
    // Your Google OAuth 2.0 Client ID
    // Get this from Google Cloud Console > APIs & Services > Credentials
    CLIENT_ID: '868080434616-6ls9uns5rd9to8qsgnditq48b1i4q923.apps.googleusercontent.com',

    // Your Google API Key (NOT Client Secret!)
    // Get this from Google Cloud Console > APIs & Services > Credentials > API keys section
    // Should start with "AIzaSy" NOT "GOCSPX-"
    API_KEY: 'AIzaSyCYiPzsiSXN_OR_qzVCm5FbYYldBzbDIoA', // Replace with actual API key starting with AIzaSy

    // OAuth 2.0 Scopes required for the application
    SCOPES: [
        'https://www.googleapis.com/auth/calendar.readonly',
        'https://www.googleapis.com/auth/meetings.space.readonly',
        'https://www.googleapis.com/auth/meetings.space.created',
        'https://www.googleapis.com/auth/userinfo.email',
        'https://www.googleapis.com/auth/user.emails.read',
        'https://www.googleapis.com/auth/contacts.readonly'
    ].join(' '),

    // Google API Discovery Documents
    DISCOVERY_DOCS: [
        'https://www.googleapis.com/discovery/v1/apis/calendar/v3/rest',
        'https://people.googleapis.com/$discovery/rest?version=v1'
        // Note: Meet API discovery document may not be publicly available
    ],

    // Application settings
    APP_SETTINGS: {
        // Rate limiting: delay between API calls (milliseconds)
        API_DELAY: 1000,

        // Maximum number of events to process in one batch
        MAX_BATCH_SIZE: 50,

        // Enable verbose logging
        VERBOSE_LOGGING: true
    }
};

// Export configuration for use in the main script
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
} else {
    window.CONFIG = CONFIG;
}
