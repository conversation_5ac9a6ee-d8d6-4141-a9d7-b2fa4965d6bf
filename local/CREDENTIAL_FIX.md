# Fix: API Key vs Client Secret

## 🚨 **Problem Identified**

You're using a **Client Secret** (`GOCSPX-...`) as an **API Key**. These are different credentials with different purposes.

## 🔧 **Quick Fix**

### Step 1: Get the Correct API Key

1. **Go to Google Cloud Console**: https://console.cloud.google.com/
2. **Navigate to**: APIs & Services → Credentials
3. **Look for "API keys" section** (NOT "OAuth 2.0 Client IDs")
4. **If no API key exists**:
   - Click "Create Credentials" → "API key"
   - Copy the generated key (starts with `AIzaSy`)
5. **If API key exists**:
   - Click on the existing API key
   - Copy the key value

### Step 2: Update Your Configuration

Replace in `local/config.js`:

```javascript
// ❌ WRONG (This is a Client Secret)
API_KEY: '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',

// ✅ CORRECT (This should be an API Key)
API_KEY: 'AIzaSyC-your-actual-api-key-here',
```

## 📋 **Credential Types Explained**

| Credential Type | Format | Purpose | Where Used |
|----------------|--------|---------|------------|
| **OAuth Client ID** | `123456789-abc.apps.googleusercontent.com` | User authentication | `CLIENT_ID` |
| **Client Secret** | `GOCSPX-...` | Server-side OAuth (NOT needed for browser) | Not used in browser apps |
| **API Key** | `AIzaSy...` | API access authentication | `API_KEY` |

## 🎯 **Your Current Setup**

✅ **Client ID**: `868080434616-6ls9uns5rd9to8qsgnditq48b1i4q923.apps.googleusercontent.com` (CORRECT)

❌ **API Key**: `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` (WRONG - this is a Client Secret)

## 🔍 **How to Find API Key in Google Cloud Console**

1. **Go to Credentials page**
2. **Look for this section**:
   ```
   API keys
   ├── [Your API Key Name]
   │   └── AIzaSyC-dK9BFk... [Show key]
   ```
3. **Click "Show key"** or the key name
4. **Copy the key** (starts with `AIzaSy`)

## 🚀 **After Fixing**

1. **Update** `local/config.js` with correct API key
2. **Refresh** the browser page
3. **Test** authentication - should work without errors

## 🔒 **Security Note**

- **Client Secrets** should NEVER be used in browser applications
- **API Keys** are safe for browser use (but restrict them to your domain)
- **Client IDs** are public and safe for browser use

## 🧪 **Test Your Fix**

After updating the API key:

```bash
cd local
python server.py
# Open: http://localhost:8000/test-auth.html
```

You should see:
- ✅ "Google API client initialized" 
- ✅ "Google Identity Services initialized"
- ✅ No 400/404 errors in console

## 📞 **Still Having Issues?**

If you can't find an API key:

1. **Create one**: Credentials → Create Credentials → API key
2. **Restrict it** (optional): Limit to Calendar API and your domain
3. **Copy and use** the generated key

The key should look like: `AIzaSyC-dK9BFkE7rX8Q9vZ2mN5pL3kJ1hG6fD4`
