# Local Version Setup Guide

This guide explains how to set up and run the Google Meet Attendance Export application locally in your browser.

## Prerequisites

1. Google account with access to Google Calendar
2. Google Workspace account with Google Meet (for accessing Meet API)
3. Basic understanding of HTML/JavaScript
4. A local web server (or ability to run one)

## Setup Steps

### 1. Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Note your project ID for later use

### 2. Enable Required APIs

In the Google Cloud Console:

1. Go to **APIs & Services** > **Library**
2. Search for and enable:
   - **Google Calendar API**
   - **Google Meet API** (if available for your organization)

### 3. Create OAuth 2.0 Credentials

1. Go to **APIs & Services** > **Credentials**
2. Click **Create Credentials** > **OAuth client ID**
3. If prompted, configure the OAuth consent screen:
   - Choose **External** (unless you're in a Google Workspace organization)
   - Fill in required fields (App name, User support email, Developer contact)
   - Add scopes:
     - `https://www.googleapis.com/auth/calendar.readonly`
     - `https://www.googleapis.com/auth/meetings.space.readonly`
4. For Application type, choose **Web application**
5. Add authorized JavaScript origins:
   - `http://localhost:8000` (or your local server URL)
   - `http://127.0.0.1:8000`
6. Download the client configuration

### 4. Create API Key

1. In **APIs & Services** > **Credentials**
2. Click **Create Credentials** > **API key**
3. Copy the API key
4. (Optional) Restrict the key to specific APIs for security

### 5. Configure the Application

1. Open `local/local-script.js`
2. Replace the placeholder values at the top:
   ```javascript
   const CLIENT_ID = 'your-client-id.apps.googleusercontent.com';
   const API_KEY = 'your-api-key';
   ```

### 6. Set Up Local Web Server

The application must be served over HTTP/HTTPS (not file://) due to Google API requirements.

#### Option A: Python (if installed)
```bash
# Python 3
cd local
python -m http.server 8000

# Python 2
cd local
python -m SimpleHTTPServer 8000
```

#### Option B: Node.js (if installed)
```bash
# Install a simple server
npm install -g http-server

# Run server
cd local
http-server -p 8000
```

#### Option C: PHP (if installed)
```bash
cd local
php -S localhost:8000
```

### 7. Access the Application

1. Open your browser
2. Go to `http://localhost:8000`
3. You should see the Google Meet Attendance Export interface

## Usage

### Authentication

1. Click **"Sign In with Google"**
2. Grant the requested permissions
3. You should see "Authenticated" status

### Demo Mode

To test the application without real data:
1. Click **"Load Demo Data"**
2. Click **"Process Events"**
3. View the sample results

### Real Data Processing

1. Ensure you're authenticated
2. Enter Google Calendar event IDs in the text area
3. Click **"Process Events"**
4. View results and export if needed

## File Structure

```
local/
├── index.html          # Main HTML interface
├── local-script.js     # JavaScript for local version
├── config.js          # Configuration file (optional)
├── LOCAL_SETUP.md     # This setup guide
└── ../style.css       # Shared CSS (from parent directory)
```

## Troubleshooting

### Common Issues

#### 1. "Please configure your Google API credentials"
- **Cause**: CLIENT_ID or API_KEY not set in local-script.js
- **Solution**: Follow steps 3-5 above to get and configure credentials

#### 2. "Failed to initialize Google API"
- **Cause**: Invalid credentials or API not enabled
- **Solution**: 
  - Verify CLIENT_ID and API_KEY are correct
  - Ensure APIs are enabled in Google Cloud Console
  - Check browser console for detailed error messages

#### 3. "Sign in failed" or authentication issues
- **Cause**: OAuth configuration problems
- **Solution**:
  - Verify authorized JavaScript origins include your local server URL
  - Ensure OAuth consent screen is properly configured
  - Try clearing browser cache and cookies

#### 4. "Meet API access from browser may be limited"
- **Cause**: Google Meet API has limited browser support
- **Solution**: 
  - This is expected - the local version shows this warning
  - Use the Apps Script version for full Meet API access
  - Demo mode will still work to test the interface

#### 5. CORS errors
- **Cause**: Accessing the file directly (file://) instead of through a web server
- **Solution**: Use one of the local server options in step 6

### Debugging

1. Open browser Developer Tools (F12)
2. Check the Console tab for error messages
3. Enable verbose logging by setting `VERBOSE_LOGGING: true` in config
4. Use the Network tab to monitor API calls

## Limitations of Local Version

1. **Meet API Access**: Limited browser support for Google Meet API
2. **Authentication**: Requires OAuth setup (more complex than Apps Script)
3. **CORS**: Must be served from a web server, not opened as a file
4. **Rate Limits**: Subject to Google API quotas for your project

## Security Considerations

1. **API Key Security**: 
   - Don't commit API keys to version control
   - Consider restricting API key to specific domains
   - Use environment variables for production deployments

2. **OAuth Security**:
   - Only add necessary authorized origins
   - Regularly review OAuth consent screen settings
   - Monitor API usage in Google Cloud Console

## Production Deployment

For production use:

1. **Domain Setup**: Configure OAuth for your actual domain
2. **HTTPS**: Ensure your site uses HTTPS
3. **Environment Variables**: Use secure methods to store credentials
4. **Monitoring**: Set up logging and monitoring for API usage
5. **Rate Limiting**: Implement client-side rate limiting

## Comparison with Apps Script Version

| Feature | Local Version | Apps Script Version |
|---------|---------------|-------------------|
| Setup Complexity | High (OAuth setup) | Low (built-in auth) |
| Meet API Access | Limited | Full access |
| Customization | High | Medium |
| Hosting | Self-hosted | Google-hosted |
| Authentication | OAuth 2.0 | Google Apps Script |
| Rate Limits | Your project quotas | Apps Script quotas |

## Support

For issues specific to the local version:
1. Check browser console for errors
2. Verify Google Cloud Console configuration
3. Test with demo mode first
4. Consider using the Apps Script version for simpler setup
