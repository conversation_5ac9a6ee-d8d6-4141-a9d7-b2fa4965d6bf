# Google Identity Services (OAuth 2.0) Setup Guide

Quick guide to set up Google Identity Services (the new OAuth 2.0 system) for the local version.

**Note**: This application now uses Google Identity Services (GIS), which replaced the deprecated gapi.auth2 library.

## Step 1: Google Cloud Console Setup

### 1.1 Create Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click "New Project" or select existing project
3. Note your Project ID

### 1.2 Enable APIs
1. Go to **APIs & Services** > **Library**
2. Search and enable:
   - ✅ **Google Calendar API**
   - ✅ **Google Meet API** (if available)

### 1.3 Configure OAuth Consent Screen
1. Go to **APIs & Services** > **OAuth consent screen**
2. Choose **External** (unless you're in a Google Workspace org)
3. Fill required fields:
   - **App name**: "Meet Attendance Export"
   - **User support email**: Your email
   - **Developer contact**: Your email
4. **Scopes**: Add these scopes:
   - `https://www.googleapis.com/auth/calendar.readonly`
   - `https://www.googleapis.com/auth/meetings.space.readonly`
5. **Test users**: Add your email for testing

### 1.4 Create OAuth Client ID
1. Go to **APIs & Services** > **Credentials**
2. Click **Create Credentials** > **OAuth client ID**
3. Choose **Web application**
4. **Name**: "Meet Attendance Local"
5. **Authorized JavaScript origins**:
   - `http://localhost:8000`
   - `http://127.0.0.1:8000`
   - Add your domain if deploying elsewhere
6. **Authorized redirect URIs**: (leave empty for JavaScript origins)
7. Click **Create**
8. **Copy the Client ID** (looks like: `123456789-abc123.apps.googleusercontent.com`)

### 1.5 Create API Key
1. Still in **Credentials**
2. Click **Create Credentials** > **API key**
3. **Copy the API Key**
4. (Optional) Click **Restrict Key** and limit to Calendar/Meet APIs

## Step 2: Configure the Application

### Option A: Edit config.js (Recommended)
```javascript
const CONFIG = {
    CLIENT_ID: 'your-client-id.apps.googleusercontent.com',
    API_KEY: 'your-api-key',
    // ... rest stays the same
};
```

### Option B: Edit local-script.js directly
```javascript
const CLIENT_ID = 'your-client-id.apps.googleusercontent.com';
const API_KEY = 'your-api-key';
```

## Step 3: Test OAuth Flow

1. **Start local server**:
   ```bash
   cd local
   python server.py
   ```

2. **Open application**: http://localhost:8000

3. **Test authentication**:
   - Click "Sign In with Google"
   - Grant permissions in popup
   - Should see "Authenticated" status

4. **Test API access**:
   - Enter a real Google Calendar event ID
   - Click "Process Events"
   - Should retrieve calendar data

## OAuth Scopes Explained

The application requests these permissions:

### `calendar.readonly`
- **Purpose**: Read Google Calendar events
- **Access**: Event details, conference data, attendees
- **Required**: Yes (core functionality)

### `meetings.space.readonly`
- **Purpose**: Read Google Meet attendance data
- **Access**: Conference records, participant sessions
- **Required**: Yes (for attendance data)
- **Note**: May have limited browser support

## Security Best Practices

### 1. Restrict API Key
- Limit to specific APIs (Calendar, Meet)
- Restrict to specific domains/IPs if possible

### 2. OAuth Client Security
- Only add necessary authorized origins
- Use HTTPS in production
- Regularly review OAuth consent screen

### 3. Token Management
- Tokens are managed automatically by Google API client
- Tokens expire and refresh automatically
- Sign out clears tokens from browser

## Troubleshooting OAuth Issues

### "Error 400: redirect_uri_mismatch"
- **Cause**: Authorized JavaScript origins not configured
- **Fix**: Add `http://localhost:8000` to authorized origins

### "Error 403: access_denied"
- **Cause**: OAuth consent screen not configured or user denied
- **Fix**: Complete consent screen setup, ensure user grants permissions

### "Error 401: unauthorized"
- **Cause**: Invalid API key or client ID
- **Fix**: Verify credentials are correct and APIs are enabled

### "This app isn't verified"
- **Cause**: OAuth consent screen in testing mode
- **Fix**: Normal for development, click "Advanced" > "Go to app (unsafe)"

### "API key not valid"
- **Cause**: API key restrictions or wrong key
- **Fix**: Check API key restrictions, ensure Calendar API is enabled

## Production Deployment

For production use:

1. **Domain Configuration**:
   - Add your production domain to authorized origins
   - Update OAuth consent screen with production details

2. **App Verification**:
   - Submit for Google verification if using sensitive scopes
   - Or keep in testing mode for internal use

3. **HTTPS Required**:
   - Google requires HTTPS for production OAuth
   - Use SSL certificates for your domain

## Testing Without Real Setup

If you want to test the interface without OAuth setup:

1. **Use Demo Mode**: Click "Load Demo Data"
2. **Mock Authentication**: The app will work with sample data
3. **Interface Testing**: All UI features work without real API calls

## OAuth vs Apps Script

| Feature | Local OAuth | Apps Script |
|---------|-------------|-------------|
| **Setup** | Complex (OAuth flow) | Simple (built-in) |
| **User Experience** | Google sign-in popup | Automatic |
| **Token Management** | Manual (gapi handles) | Automatic |
| **Permissions** | Explicit user consent | Implicit |
| **Customization** | Full control | Limited |

The OAuth setup gives you full control but requires more configuration than the Apps Script version.
