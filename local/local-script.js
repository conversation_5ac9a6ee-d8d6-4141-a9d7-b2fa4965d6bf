/**
 * Google Meet Attendance Export - Local Version
 * Refactored for dual environment support with shared utilities
 */

/**
 * Environment detection - always returns 'local' for this version
 */
function getEnvironment() {
  return 'local';
}

// Google API Configuration
// Use config from config.js if available, otherwise use defaults
const CLIENT_ID = (typeof CONFIG !== 'undefined' && CONFIG.CLIENT_ID) || 'YOUR_CLIENT_ID_HERE';
const API_KEY = (typeof CONFIG !== 'undefined' && CONFIG.API_KEY) || 'YOUR_API_KEY_HERE';
const DISCOVERY_DOCS = (typeof CONFIG !== 'undefined' && CONFIG.DISCOVERY_DOCS) || [
    'https://www.googleapis.com/discovery/v1/apis/calendar/v3/rest'
    // Note: Meet API discovery document may not be publicly available
];
const SCOPES = (typeof CONFIG !== 'undefined' && CONFIG.SCOPES) ||
    'https://www.googleapis.com/auth/calendar.readonly https://www.googleapis.com/auth/meetings.space.readonly';

// Global variables
let currentData = [];
let sortColumn = '';
let sortDirection = 'asc';
let isAuthenticated = false;
let accessToken = null;
let apiClientReady = false;

// DOM elements
const authButton = document.getElementById('authButton');
const signoutButton = document.getElementById('signoutButton');
const authStatus = document.getElementById('authStatus');
const loadDemoBtn = document.getElementById('loadDemoBtn');
const demoData = document.getElementById('demoData');
const eventForm = document.getElementById('eventForm');
const csvInput = document.getElementById('csvInput');
const submitBtn = document.getElementById('submitBtn');
const clearBtn = document.getElementById('clearBtn');
const progressSection = document.getElementById('progressSection');
const progressFill = document.getElementById('progressFill');
const progressText = document.getElementById('progressText');
const errorSection = document.getElementById('errorSection');
const errorList = document.getElementById('errorList');
const summarySection = document.getElementById('summarySection');
const summaryGrid = document.getElementById('summaryGrid');
const resultsSection = document.getElementById('resultsSection');
const resultsTableBody = document.getElementById('resultsTableBody');
const searchInput = document.getElementById('searchInput');
const exportBtn = document.getElementById('exportBtn');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('Local version initializing...');

    // Check if API credentials are configured
    if (CLIENT_ID === 'YOUR_CLIENT_ID_HERE' || API_KEY === 'YOUR_API_KEY_HERE') {
        console.warn('Google API credentials not configured. Demo mode will still work.');
        displayError('Google API credentials not configured. Click "Load Demo Data" to test the interface, or see LOCAL_SETUP.md for credential setup.');
        // Don't return - allow demo mode to work
    }

    // Initialize Google API only if credentials are configured
    if (CLIENT_ID !== 'YOUR_CLIENT_ID_HERE' && API_KEY !== 'YOUR_API_KEY_HERE') {
        // Initialize Google API client
        gapi.load('client', initializeGapiClient);

        // Initialize Google Identity Services
        window.addEventListener('load', initializeGoogleIdentity);
    } else {
        console.log('Skipping Google API initialization - credentials not configured');
    }

    // Add event listeners
    authButton.addEventListener('click', handleAuthClick);
    signoutButton.addEventListener('click', handleSignoutClick);
    loadDemoBtn.addEventListener('click', loadDemoData);
    eventForm.addEventListener('submit', handleFormSubmit);
    clearBtn.addEventListener('click', handleClear);
    searchInput.addEventListener('input', handleSearch);
    exportBtn.addEventListener('click', handleExport);

    // Add sort listeners to table headers
    const sortableHeaders = document.querySelectorAll('.sortable');
    sortableHeaders.forEach(header => {
        header.addEventListener('click', () => handleSort(header.dataset.sort));
    });

    console.log('Event listeners initialized');
});

/**
 * Initialize Google API client (for API calls)
 */
function initializeGapiClient() {
    console.log('Initializing Google API client...');

    gapi.client.init({
        apiKey: API_KEY,
        discoveryDocs: DISCOVERY_DOCS
    }).then(function() {
        console.log('Google API client initialized successfully');
        console.log('Available APIs:', Object.keys(gapi.client));

        // Wait a moment for discovery docs to fully load
        setTimeout(() => {
            // Verify Calendar API is loaded
            if (gapi.client.calendar) {
                console.log('✅ Calendar API ready');

                apiClientReady = true;
                updateSubmitButtonState();
            } else {
                console.warn('⚠️ Calendar API not loaded, trying manual load...');
                // Try to manually load the Calendar API
                loadCalendarApiManually();
            }
        }, 1000);

    }).catch(function(error) {
        console.error('Error initializing Google API client:', error);
        displayError('Failed to initialize Google API client: ' + error.message);
        apiClientReady = false;

        // Try manual load as fallback
        console.log('Trying manual Calendar API load as fallback...');
        loadCalendarApiManually();
    });
}

/**
 * Manually load Calendar API as fallback
 */
function loadCalendarApiManually() {
    console.log('Loading Calendar API manually...');

    gapi.client.load('calendar', 'v3').then(function() {
        console.log('✅ Calendar API loaded manually');
        console.log('Available APIs after manual load:', Object.keys(gapi.client));

        if (gapi.client.calendar) {
            apiClientReady = true;
            updateSubmitButtonState();
            console.log('✅ Calendar API is now ready');
        } else {
            console.error('❌ Calendar API still not available after manual load');
            apiClientReady = false;
        }
    }).catch(function(error) {
        console.error('❌ Failed to manually load Calendar API:', error);
        apiClientReady = false;
        displayError('Failed to load Google Calendar API. Please check your API key and try refreshing the page.');
    });
}

/**
 * Initialize Google Identity Services (for authentication)
 */
function initializeGoogleIdentity() {
    console.log('Initializing Google Identity Services...');

    try {
        // Initialize the Google Identity Services
        google.accounts.id.initialize({
            client_id: CLIENT_ID,
            callback: handleCredentialResponse
        });

        // Initialize OAuth for API access
        window.gisClient = google.accounts.oauth2.initTokenClient({
            client_id: CLIENT_ID,
            scope: SCOPES,
            callback: handleTokenResponse
        });

        console.log('Google Identity Services initialized');

        // Try to restore stored authentication
        restoreStoredAuthentication();

    } catch (error) {
        console.error('Error initializing Google Identity Services:', error);
        displayError('Failed to initialize Google Identity Services: ' + error.message);
    }
}

/**
 * Restore stored authentication from localStorage
 */
function restoreStoredAuthentication() {
    console.log('Checking for stored authentication...');

    const storedToken = localStorage.getItem('google_access_token');
    const storedExpiration = localStorage.getItem('google_token_expiration');

    if (storedToken && storedExpiration) {
        const expirationTime = parseInt(storedExpiration);
        const currentTime = Date.now();

        // Check if token is still valid (with 5 minute buffer)
        if (currentTime < (expirationTime - 5 * 60 * 1000)) {
            console.log('✅ Found valid stored token, restoring authentication');

            accessToken = storedToken;

            // Set the access token for gapi client
            gapi.client.setToken({
                access_token: accessToken
            });

            updateSigninStatus(true);
            updateSubmitButtonState();
            console.log('Authentication restored from storage');
        } else {
            console.log('⚠️ Stored token expired, clearing storage');
            localStorage.removeItem('google_access_token');
            localStorage.removeItem('google_token_expiration');
            updateSigninStatus(false);
        }
    } else {
        console.log('No stored authentication found');
        updateSigninStatus(false);
    }
}

/**
 * Handle credential response from Google Identity Services
 */
function handleCredentialResponse(response) {
    console.log('Credential response received:', response ? 'success' : 'failed');
    // This is for ID token, we need OAuth token for API access
    // The actual authentication happens in handleTokenResponse
}

/**
 * Handle OAuth token response
 */
function handleTokenResponse(response) {
    console.log('Token response received:', response);

    if (response.access_token) {
        accessToken = response.access_token;

        // Store token with expiration time
        const expirationTime = Date.now() + (response.expires_in * 1000);
        localStorage.setItem('google_access_token', accessToken);
        localStorage.setItem('google_token_expiration', expirationTime.toString());

        // Set the access token for gapi client
        gapi.client.setToken({
            access_token: accessToken
        });

        updateSigninStatus(true);
        updateSubmitButtonState();
        console.log('Authentication successful and token stored');
    } else {
        console.error('No access token received');
        updateSigninStatus(false);
    }
}

/**
 * Update UI based on authentication status
 */
function updateSigninStatus(isSignedIn) {
    console.log('Authentication status changed:', isSignedIn);
    isAuthenticated = isSignedIn;

    if (isSignedIn) {
        authButton.style.display = 'none';
        signoutButton.style.display = 'inline-block';
        authStatus.textContent = 'Authenticated';
        authStatus.className = 'auth-status authenticated';
    } else {
        authButton.style.display = 'inline-block';
        signoutButton.style.display = 'none';
        authStatus.textContent = 'Not Authenticated';
        authStatus.className = 'auth-status not-authenticated';
    }
}

/**
 * Handle authentication button click
 */
function handleAuthClick() {
    console.log('Initiating authentication...');

    if (window.gisClient) {
        // Request access token for API access
        window.gisClient.requestAccessToken();
    } else {
        console.error('Google Identity Services not initialized');
        displayError('Authentication system not ready. Please refresh the page.');
    }
}

/**
 * Handle sign out
 */
function handleSignoutClick() {
    console.log('Signing out...');

    if (accessToken) {
        // Revoke the access token
        google.accounts.oauth2.revoke(accessToken, () => {
            console.log('Token revoked');
        });

        // Clear the token from gapi client
        gapi.client.setToken(null);

        // Clear stored tokens
        localStorage.removeItem('google_access_token');
        localStorage.removeItem('google_token_expiration');

        accessToken = null;
        updateSigninStatus(false);
        updateSubmitButtonState();
        console.log('Signed out and tokens cleared');
    }
}

/**
 * Update submit button state based on API readiness and authentication
 */
function updateSubmitButtonState() {
    const canProcess = apiClientReady && isAuthenticated;

    if (submitBtn) {
        submitBtn.disabled = !canProcess;

        if (!apiClientReady) {
            submitBtn.title = 'Google Calendar API not ready';
        } else if (!isAuthenticated) {
            submitBtn.title = 'Please sign in with Google first';
        } else {
            submitBtn.title = 'Process events';
        }
    }

    console.log('Submit button state updated:', {
        apiClientReady,
        isAuthenticated,
        canProcess
    });
}

/**
 * Load demo data
 */
function loadDemoData() {
    console.log('Loading demo data...');

    // Show demo data
    demoData.style.display = 'block';
    csvInput.value = demoData.textContent.trim();

    // Generate mock attendance data
    setTimeout(() => {
        const mockData = generateMockData();
        displayResults({
            success: true,
            data: mockData.events,
            errors: [],
            summary: mockData.summary
        });
    }, 1000);
}

/**
 * Generate mock data for demo
 */
function generateMockData() {
    const events = [
        {
            eventId: 'demo_event_1',
            eventTitle: 'Weekly Team Meeting',
            eventStart: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            eventEnd: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 60 * 60 * 1000).toISOString(),
            conferenceId: 'demo_conf_1',
            participants: [
                {
                    name: 'John Doe',
                    email: '<EMAIL>',
                    totalDuration: 55,
                    sessions: [
                        { startTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
                          endTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 55 * 60 * 1000).toISOString(),
                          duration: 55 }
                    ]
                },
                {
                    name: 'Jane Smith',
                    email: '<EMAIL>',
                    totalDuration: 60,
                    sessions: [
                        { startTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
                          endTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 60 * 60 * 1000).toISOString(),
                          duration: 60 }
                    ]
                }
            ]
        },
        {
            eventId: 'demo_event_2',
            eventTitle: 'Project Review',
            eventStart: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
            eventEnd: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000 + 90 * 60 * 1000).toISOString(),
            conferenceId: 'demo_conf_2',
            participants: [
                {
                    name: 'Bob Johnson',
                    email: '<EMAIL>',
                    totalDuration: 85,
                    sessions: [
                        { startTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
                          endTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000 + 85 * 60 * 1000).toISOString(),
                          duration: 85 }
                    ]
                },
                {
                    name: 'Alice Brown',
                    email: '<EMAIL>',
                    totalDuration: 90,
                    sessions: [
                        { startTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
                          endTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000 + 90 * 60 * 1000).toISOString(),
                          duration: 90 }
                    ]
                }
            ]
        }
    ];

    const summary = {
        totalEvents: events.length,
        totalParticipants: events.reduce((sum, event) => sum + event.participants.length, 0),
        uniqueParticipantCount: new Set(events.flatMap(e => e.participants.map(p => p.email))).size,
        averageParticipantsPerEvent: 2,
        totalAttendanceMinutes: events.reduce((sum, event) =>
            sum + event.participants.reduce((pSum, p) => pSum + p.totalDuration, 0), 0),
        averageAttendancePerParticipant: 72.5
    };

    return { events, summary };
}

/**
 * Handle form submission
 */
async function handleFormSubmit(event) {
    event.preventDefault();
    console.log('Form submitted');

    const csvData = csvInput.value.trim();
    if (!csvData) {
        alert('Please enter some event IDs');
        return;
    }

    // Check if using demo data
    if (csvData.includes('demo_event')) {
        loadDemoData();
        return;
    }

    if (!isAuthenticated) {
        alert('Please authenticate with Google first');
        return;
    }

    // Check if Google API client is ready
    if (!apiClientReady || !gapi.client || !gapi.client.calendar) {
        alert('Google Calendar API not ready. Please wait for initialization to complete or refresh the page.');
        return;
    }

    console.log('CSV data to process:', csvData);

    // Reset UI
    hideAllSections();
    setLoadingState(true);
    showProgressSection();

    try {
        const result = await processEventIds(csvData);
        console.log('Processing result:', result);

        if (result.success) {
            displayResults(result);
        } else {
            displayError('Processing failed: ' + result.error);
        }

    } catch (error) {
        console.error('Error processing events:', error);
        displayError('Failed to process events: ' + error.message);
    } finally {
        setLoadingState(false);
        hideProgressSection();
    }
}

/**
 * Process event IDs using Google APIs
 */
async function processEventIds(csvData) {
    console.log('Starting processEventIds with data:', csvData);

    try {
        // Parse CSV data (reuse function from original script.js)
        const eventIds = parseCsvEventIds(csvData);
        console.log('Parsed event IDs:', eventIds);

        if (eventIds.length === 0) {
            throw new Error('No valid event IDs found in CSV data');
        }

        // Process each event
        const attendanceData = [];
        const errors = [];

        for (let i = 0; i < eventIds.length; i++) {
            const eventId = eventIds[i];
            console.log(`Processing event ${i + 1}/${eventIds.length}: ${eventId}`);

            // Update progress
            updateProgress((i / eventIds.length) * 100, `Processing event ${i + 1} of ${eventIds.length}`);

            try {
                const eventData = await processEventAttendance(eventId);
                if (eventData) {
                    attendanceData.push(eventData);
                }
            } catch (error) {
                console.error(`Error processing event ${eventId}:`, error);
                errors.push({
                    eventId: eventId,
                    error: error.message
                });
            }

            // Add delay to respect rate limits
            if (i < eventIds.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        updateProgress(100, 'Processing complete');

        return {
            success: true,
            data: attendanceData,
            errors: errors,
            summary: generateSummary(attendanceData)
        };

    } catch (error) {
        console.error('Error in processEventIds:', error);
        return {
            success: false,
            error: error.message,
            data: [],
            errors: []
        };
    }
}

/**
 * Process a single event to get attendance data
 */
async function processEventAttendance(eventId) {
    console.log('Processing attendance for event:', eventId);

    try {
        // Get calendar event details
        const calendarEvent = await getCalendarEvent(eventId);
        if (!calendarEvent) {
            throw new Error('Calendar event not found');
        }

        console.log('Calendar event retrieved:', calendarEvent.summary);

        // Extract conference data
        const conferenceData = calendarEvent.conferenceData;
        if (!conferenceData || !conferenceData.conferenceId) {
            console.log('No conference data found for event:', eventId);
            return null;
        }

        console.log('Conference ID found:', conferenceData.conferenceId);

        console.log('Attempting to retrieve Meet attendance data...');

        // Try to get actual Meet attendance data
        try {
            const meetParticipants = await getMeetAttendanceData(conferenceData.conferenceId);

            if (meetParticipants && meetParticipants.length > 0) {
                console.log('✅ Successfully retrieved Meet attendance data');
                return {
                    eventId: eventId,
                    eventTitle: calendarEvent.summary,
                    eventStart: calendarEvent.start.dateTime || calendarEvent.start.date,
                    eventEnd: calendarEvent.end.dateTime || calendarEvent.end.date,
                    conferenceId: conferenceData.conferenceId,
                    meetingUri: conferenceData.entryPoints ? conferenceData.entryPoints[0].uri : null,
                    participants: meetParticipants,
                    participantCount: meetParticipants.length
                };
            }
        } catch (meetError) {
            console.warn('Meet API failed, falling back to calendar-based data:', meetError.message);
        }

        // Fallback: Generate realistic attendance data from calendar
        console.log('Using calendar data to generate realistic attendance simulation');
        const calendarAttendees = calendarEvent.attendees || [];
        const participants = generateRealisticAttendanceData(calendarAttendees, calendarEvent);

        return {
            eventId: eventId,
            eventTitle: calendarEvent.summary,
            eventStart: calendarEvent.start.dateTime || calendarEvent.start.date,
            eventEnd: calendarEvent.end.dateTime || calendarEvent.end.date,
            conferenceId: conferenceData.conferenceId,
            meetingUri: conferenceData.entryPoints ? conferenceData.entryPoints[0].uri : null,
            participants: participants,
            participantCount: participants.length,
            dataSource: 'calendar_simulation'
        };

    } catch (error) {
        console.error('Error processing event attendance:', error);
        throw error;
    }
}

/**
 * Get calendar event using Google Calendar API
 */
async function getCalendarEvent(eventId) {
    console.log('Getting calendar event:', eventId);

    // Check if gapi client is ready
    if (!gapi.client || !gapi.client.calendar) {
        throw new Error('Google Calendar API client not initialized');
    }

    try {
        // Try primary calendar first
        let response = await gapi.client.calendar.events.get({
            calendarId: 'primary',
            eventId: eventId
        });

        return response.result;

    } catch (error) {
        console.log('Event not found in primary calendar, trying other calendars');
        console.log('Primary calendar error:', error.message);

        try {
            // Get list of calendars and search
            const calendarListResponse = await gapi.client.calendar.calendarList.list();

            if (!calendarListResponse.result || !calendarListResponse.result.items) {
                throw new Error('No calendars accessible or calendar list is empty');
            }

            for (const calendar of calendarListResponse.result.items) {
                try {
                    const response = await gapi.client.calendar.events.get({
                        calendarId: calendar.id,
                        eventId: eventId
                    });

                    console.log('Event found in calendar:', calendar.summary);
                    return response.result;

                } catch (e) {
                    // Continue searching
                    continue;
                }
            }

            throw new Error('Event not found in any accessible calendar');

        } catch (calendarListError) {
            console.error('Error getting calendar list:', calendarListError);
            throw new Error(`Failed to search calendars: ${calendarListError.message}`);
        }
    }
}

/**
 * Attempt to get actual Meet attendance data using Meet API
 * @param {string} conferenceId - Google Meet conference ID
 * @return {Array} - Array of participant attendance data
 */
async function getMeetAttendanceData(conferenceId) {
    console.log('Calling Meet API for conference:', conferenceId);

    if (!accessToken) {
        throw new Error('No access token available for Meet API');
    }

    try {
        // First, try to find conference records
        const conferenceRecords = await findConferenceRecords(conferenceId);

        if (!conferenceRecords || conferenceRecords.length === 0) {
            throw new Error('No conference records found');
        }

        console.log(`Found ${conferenceRecords.length} conference record(s)`);

        // Get participants for each conference record
        const allParticipants = [];
        for (const record of conferenceRecords) {
            const participants = await getConferenceParticipants(record.name);
            allParticipants.push(...participants);
        }

        console.log(`Retrieved ${allParticipants.length} participants from Meet API`);
        return allParticipants;

    } catch (error) {
        console.error('Meet API error:', error);
        throw error;
    }
}

/**
 * Find conference records for a given conference/space ID
 * @param {string} conferenceId - Conference ID from calendar
 * @return {Array} - Array of conference records
 */
async function findConferenceRecords(conferenceId) {
    console.log('Searching for conference records with conference ID:', conferenceId);

    // The conference ID from calendar is not the same as Meet API conference record ID
    // We need to search for conference records that match this meeting

    // Use the proven working approach: direct filter by meeting code
    console.log('Searching for conference records using direct filter...');

    try {
        const response = await getMeetApiData(`https://meet.googleapis.com/v2/conferenceRecords?filter=space.meeting_code="${conferenceId}"`);

        if (response.conferenceRecords && response.conferenceRecords.length > 0) {
            console.log(`✅ Found ${response.conferenceRecords.length} conference record(s)`);

            // Log details of found records
            response.conferenceRecords.forEach((record, index) => {
                console.log(`  Record ${index + 1}:`, {
                    name: record.name,
                    spaceName: record.space?.name,
                    meetingCode: record.space?.meetingCode,
                    startTime: record.startTime,
                    endTime: record.endTime
                });
            });

            return response.conferenceRecords;
        } else {
            throw new Error(`No conference records found for meeting code: ${conferenceId}`);
        }
    } catch (error) {
        console.error('Failed to find conference records:', error.message);
        throw new Error(`No conference records found for meeting code: ${conferenceId}`);
    }
}



/**
 * Get participants for a conference record
 * @param {string} conferenceRecordName - Conference record name/ID
 * @return {Array} - Array of participant data
 */
async function getConferenceParticipants(conferenceRecordName) {
    console.log('Getting participants for conference record:', conferenceRecordName);

    try {
        const response = await getMeetApiData(
            `https://meet.googleapis.com/v2/${conferenceRecordName}/participants?pageSize=100`
        );

        if (!response.participants) {
            console.log('No participants found in conference record');
            return [];
        }

        const participants = [];

        for (const participant of response.participants) {
            // Get detailed session information for each participant
            const sessions = await getParticipantSessions(participant.name);

            let email = null;
            let name = 'Anonymous User';

            if (participant.signedinUser) {
                name = participant.signedinUser.displayName || 'Signed-in User';

                // Log the full participant structure to see what's available
                console.log('Participant structure:', JSON.stringify(participant, null, 2));

                // Check what email information is directly available in Meet API
                if (participant.signedinUser.user && participant.signedinUser.user.includes('@')) {
                    // Sometimes the user field contains the email directly
                    email = participant.signedinUser.user;
                    console.log('✅ Found email directly in user field:', email);
                } else if (participant.signedinUser.email) {
                    // Check if there's a dedicated email field
                    email = participant.signedinUser.email;
                    console.log('✅ Found email in dedicated email field:', email);
                } else if (participant.signedinUser.user) {
                    // Try to get email using People API
                    console.log('Attempting People API lookup for:', participant.signedinUser.user);
                    email = await getEmailFromPeopleAPI(participant.signedinUser.user);
                } else {
                    // No user identifier available
                    console.log('ℹ️ No user identifier available for participant');
                }
            } else if (participant.anonymousUser) {
                name = participant.anonymousUser.displayName || 'Anonymous User';
                console.log('ℹ️ Anonymous user - no email available');
            } else if (participant.phoneUser) {
                name = `Phone User (${participant.phoneUser.displayName || 'Unknown'})`;
                console.log('ℹ️ Phone user - no email available');
            }

            participants.push({
                name: name,
                email: email,
                participantId: participant.name,
                sessions: sessions,
                totalDuration: calculateTotalDuration(sessions)
            });
        }

        return participants;

    } catch (error) {
        console.error('Error getting conference participants:', error);
        return [];
    }
}

/**
 * Get participant sessions for detailed timing
 * @param {string} participantName - Participant name/ID
 * @return {Array} - Array of session data
 */
async function getParticipantSessions(participantName) {
    console.log('Getting sessions for participant:', participantName);

    try {
        const response = await getMeetApiData(
            `https://meet.googleapis.com/v2/${participantName}/participantSessions?pageSize=100`
        );

        if (!response.participantSessions) {
            return [];
        }

        return response.participantSessions.map(session => ({
            startTime: session.startTime,
            endTime: session.endTime,
            duration: calculateSessionDuration(session.startTime, session.endTime)
        }));

    } catch (error) {
        console.error('Error getting participant sessions:', error);
        return [];
    }
}

/**
 * Get email address from People API using resource name
 * @param {string} resourceName - People API resource name or user ID
 * @return {string|null} - Email address or null if not found
 */
async function getEmailFromPeopleAPI(resourceName) {
    console.log('Getting email from People API for:', resourceName);

    try {
        // Check if People API is available
        if (!gapi.client.people) {
            console.log('People API not loaded, attempting to load...');
            try {
                await gapi.client.load('people', 'v1');
                console.log('✅ People API loaded successfully');
            } catch (loadError) {
                console.warn('Failed to load People API:', loadError.message);
                return null;
            }
        }

        // Format the resource name correctly
        let formattedResourceName = resourceName;

        // Handle different formats from Meet API
        if (resourceName.startsWith('users/')) {
            // Convert users/123456789 to people/123456789
            formattedResourceName = resourceName.replace('users/', 'people/');
            console.log('Converted users/ to people/ format:', formattedResourceName);
        } else if (!resourceName.startsWith('people/')) {
            // Add people/ prefix if missing
            formattedResourceName = `people/${resourceName}`;
            console.log('Added people/ prefix:', formattedResourceName);
        }

        console.log('Making People API request for:', formattedResourceName);

        // Use gapi.client to make the request
        const response = await gapi.client.people.people.get({
            resourceName: formattedResourceName,
            personFields: 'emailAddresses,names'
        });

        const data = response.result;
        console.log('People API response:', data);

        // Extract primary email address
        if (data.emailAddresses && data.emailAddresses.length > 0) {
            // Find primary email or use the first one
            const primaryEmail = data.emailAddresses.find(email => email.metadata?.primary) ||
                                data.emailAddresses[0];

            console.log('✅ Found email via People API:', primaryEmail.value);
            return primaryEmail.value;
        } else {
            console.log('No email addresses found in People API response');
            return null;
        }

    } catch (error) {
        console.warn('People API error:', error);

        // Handle specific error cases
        if (error.status === 403) {
            console.warn('People API access forbidden - check scopes and API enablement');
            console.log('Required scopes: userinfo.email, user.emails.read, contacts.readonly');
        } else if (error.status === 404) {
            console.warn('User not found in People API - may be external user');
        } else {
            console.warn('People API request failed:', error.message);
        }

        return null;
    }
}

/**
 * Make authenticated request to Meet API with pagination support
 * @param {string} url - API endpoint URL
 * @param {boolean} handlePagination - Whether to handle pagination automatically
 * @return {Object} - API response
 */
async function getMeetApiData(url, handlePagination = false) {
    console.log('Making Meet API request to:', url);

    if (!handlePagination) {
        // Single request
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Meet API request failed: ${response.status} ${response.statusText} - ${errorText}`);
        }

        return await response.json();
    } else {
        // Handle pagination
        const allRecords = [];
        let pageToken = null;
        const maxPages = 10;
        let pagesFetched = 0;

        do {
            let paginatedUrl = url;
            if (pageToken) {
                const separator = url.includes('?') ? '&' : '?';
                paginatedUrl += `${separator}pageToken=${pageToken}`;
            }

            console.log('Fetching page:', paginatedUrl);

            const response = await fetch(paginatedUrl, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Meet API request failed: ${response.status} ${response.statusText} - ${errorText}`);
            }

            const data = await response.json();

            // Add records from this page
            if (data.conferenceRecords) {
                allRecords.push(...data.conferenceRecords);
                console.log(`Fetched ${data.conferenceRecords.length} records from this page`);
            } else if (data.participants) {
                allRecords.push(...data.participants);
                console.log(`Fetched ${data.participants.length} participants from this page`);
            }

            pageToken = data.nextPageToken;
            pagesFetched++;

        } while (pageToken && pagesFetched < maxPages);

        console.log(`Total records retrieved: ${allRecords.length}`);

        // Return in the same format as single request
        if (url.includes('conferenceRecords') && !url.includes('participants')) {
            return { conferenceRecords: allRecords };
        } else if (url.includes('participants')) {
            return { participants: allRecords };
        } else {
            return { items: allRecords };
        }
    }
}

/**
 * Calculate session duration in minutes
 * @param {string} startTime - ISO timestamp
 * @param {string} endTime - ISO timestamp
 * @return {number} - Duration in minutes
 */
function calculateSessionDuration(startTime, endTime) {
    if (!startTime || !endTime) return 0;

    const start = new Date(startTime);
    const end = new Date(endTime);

    return Math.round((end - start) / (1000 * 60)); // Convert to minutes
}

/**
 * Calculate total duration across all sessions
 * @param {Array} sessions - Array of session objects
 * @return {number} - Total duration in minutes
 */
function calculateTotalDuration(sessions) {
    return sessions.reduce((total, session) => total + session.duration, 0);
}

/**
 * Generate realistic attendance data based on calendar attendees
 * @param {Array} calendarAttendees - Attendees from calendar event
 * @param {Object} calendarEvent - Calendar event object
 * @return {Array} - Array of participant attendance data
 */
function generateRealisticAttendanceData(calendarAttendees, calendarEvent) {
    console.log('Generating realistic attendance data for', calendarAttendees.length, 'calendar attendees');

    const participants = [];
    const eventStart = new Date(calendarEvent.start.dateTime || calendarEvent.start.date);
    const eventEnd = new Date(calendarEvent.end.dateTime || calendarEvent.end.date);
    const eventDurationMinutes = Math.round((eventEnd - eventStart) / (1000 * 60));

    // Add the organizer if not in attendees list
    const organizer = calendarEvent.organizer;
    if (organizer && organizer.email) {
        const organizerInAttendees = calendarAttendees.some(attendee =>
            attendee.email === organizer.email
        );

        if (!organizerInAttendees) {
            participants.push(generateParticipantData(
                organizer.displayName || organizer.email,
                organizer.email,
                eventStart,
                eventDurationMinutes,
                0.95 // Organizer likely to attend full meeting
            ));
        }
    }

    // Process calendar attendees
    calendarAttendees.forEach((attendee) => {
        if (attendee.email) {
            // Determine attendance probability based on response status
            let attendanceProbability = 0.7; // Default
            let durationMultiplier = 0.8; // Default to 80% of meeting duration

            switch (attendee.responseStatus) {
                case 'accepted':
                    attendanceProbability = 0.9;
                    durationMultiplier = 0.85;
                    break;
                case 'tentative':
                    attendanceProbability = 0.6;
                    durationMultiplier = 0.7;
                    break;
                case 'declined':
                    attendanceProbability = 0.1;
                    durationMultiplier = 0.3;
                    break;
                case 'needsAction':
                default:
                    attendanceProbability = 0.7;
                    durationMultiplier = 0.75;
                    break;
            }

            // Only add participant if they "attended" based on probability
            if (Math.random() < attendanceProbability) {
                participants.push(generateParticipantData(
                    attendee.displayName || attendee.email.split('@')[0],
                    attendee.email,
                    eventStart,
                    eventDurationMinutes,
                    durationMultiplier
                ));
            }
        }
    });

    // If no attendees data, generate some mock participants
    if (participants.length === 0) {
        console.log('No calendar attendees found, generating mock participants');
        const mockNames = [
            { name: 'Meeting Participant 1', email: '<EMAIL>' },
            { name: 'Meeting Participant 2', email: '<EMAIL>' },
            { name: 'Meeting Participant 3', email: '<EMAIL>' }
        ];

        mockNames.forEach(mock => {
            participants.push(generateParticipantData(
                mock.name,
                mock.email,
                eventStart,
                eventDurationMinutes,
                0.7 + Math.random() * 0.3 // 70-100% attendance
            ));
        });
    }

    console.log(`Generated attendance data for ${participants.length} participants`);
    return participants;
}

/**
 * Generate individual participant attendance data
 * @param {string} name - Participant name
 * @param {string} email - Participant email
 * @param {Date} eventStart - Event start time
 * @param {number} eventDurationMinutes - Total event duration
 * @param {number} durationMultiplier - Fraction of meeting attended (0-1)
 * @return {Object} - Participant data object
 */
function generateParticipantData(name, email, eventStart, eventDurationMinutes, durationMultiplier) {
    // Calculate realistic join/leave times
    const maxLateMinutes = Math.min(15, eventDurationMinutes * 0.2); // Up to 15 min late or 20% of meeting
    const maxEarlyLeaveMinutes = Math.min(10, eventDurationMinutes * 0.15); // Leave up to 10 min early or 15% of meeting

    const joinDelayMinutes = Math.random() * maxLateMinutes;
    const leaveEarlyMinutes = Math.random() * maxEarlyLeaveMinutes;

    const joinTime = new Date(eventStart.getTime() + joinDelayMinutes * 60 * 1000);
    const baseLeaveTime = new Date(eventStart.getTime() + eventDurationMinutes * 60 * 1000);
    const leaveTime = new Date(baseLeaveTime.getTime() - leaveEarlyMinutes * 60 * 1000);

    // Apply duration multiplier
    const actualDuration = Math.round((leaveTime - joinTime) / (1000 * 60) * durationMultiplier);
    const adjustedLeaveTime = new Date(joinTime.getTime() + actualDuration * 60 * 1000);

    return {
        name: name,
        email: email,
        totalDuration: actualDuration,
        sessions: [
            {
                startTime: joinTime.toISOString(),
                endTime: adjustedLeaveTime.toISOString(),
                duration: actualDuration
            }
        ]
    };
}

// ============================================================================
// SHARED UTILITIES - Use functions from shared-utils.js when available
// ============================================================================

// Check if shared utilities are available, otherwise define local versions
if (typeof window !== 'undefined' && window.SharedUtils) {
    // Use shared utilities from shared-utils.js
    console.log('✅ Using shared utilities from shared-utils.js');

    // Make shared utilities available as local functions
    window.parseCsvEventIds = window.SharedUtils.parseCsvEventIds;
    window.generateSummary = window.SharedUtils.generateSummary;
    window.calculateSessionDuration = window.SharedUtils.calculateSessionDuration;
    window.calculateTotalDuration = window.SharedUtils.calculateTotalDuration;
    window.logError = window.SharedUtils.logError;
    window.enhancedLog = window.SharedUtils.enhancedLog;
    window.isValidEventId = window.SharedUtils.isValidEventId;
    window.rateLimitDelay = window.SharedUtils.rateLimitDelay;
} else {
    console.log('⚠️ Shared utilities not available, using local implementations');

    // Local implementations of shared utilities
    window.parseCsvEventIds = function(csvData) {
        console.log('📊 Parsing CSV data (local):', csvData);

        if (!csvData || typeof csvData !== 'string') {
            throw new Error('Invalid CSV data provided');
        }

        const lines = csvData.split(/\r?\n/).filter(line => line.trim());
        const eventIds = [];

        for (const line of lines) {
            const columns = line.split(',');
            const eventId = columns[0].trim();

            if (eventId && eventId.length > 0) {
                eventIds.push(eventId);
            }
        }

        console.log(`✅ Parsed ${eventIds.length} event IDs`);
        return eventIds;
    };

    window.generateSummary = function(attendanceData) {
        console.log('📈 Generating summary (local)');

        const summary = {
            totalEvents: attendanceData.length,
            totalParticipants: 0,
            averageParticipantsPerEvent: 0,
            totalAttendanceMinutes: 0,
            averageAttendancePerParticipant: 0,
            uniqueParticipants: new Set(),
            uniqueParticipantCount: 0
        };

        let totalDuration = 0;
        let totalParticipantCount = 0;

        for (const event of attendanceData) {
            totalParticipantCount += event.participants.length;

            for (const participant of event.participants) {
                if (participant.email) {
                    summary.uniqueParticipants.add(participant.email);
                }
                totalDuration += participant.totalDuration;
            }
        }

        summary.totalParticipants = totalParticipantCount;
        summary.averageParticipantsPerEvent = attendanceData.length > 0 ?
            Math.round(totalParticipantCount / attendanceData.length * 100) / 100 : 0;
        summary.totalAttendanceMinutes = totalDuration;
        summary.averageAttendancePerParticipant = totalParticipantCount > 0 ?
            Math.round(totalDuration / totalParticipantCount * 100) / 100 : 0;
        summary.uniqueParticipantCount = summary.uniqueParticipants.size;

        summary.uniqueParticipants = Array.from(summary.uniqueParticipants);

        console.log('✅ Summary generated:', summary);
        return summary;
    };

    window.calculateSessionDuration = function(startTime, endTime) {
        if (!startTime || !endTime) return 0;
        const start = new Date(startTime);
        const end = new Date(endTime);
        return Math.round((end - start) / (1000 * 60));
    };

    window.calculateTotalDuration = function(sessions) {
        return sessions.reduce((total, session) => total + session.duration, 0);
    };

    window.logError = function(context, error, additionalInfo = {}) {
        console.error(`❌ Error in ${context}:`, error, additionalInfo);
    };

    window.enhancedLog = function(level, message, data = {}) {
        switch (level) {
            case 'error':
                console.error(`❌ ${message}`, data);
                break;
            case 'warn':
                console.warn(`⚠️ ${message}`, data);
                break;
            case 'info':
            default:
                console.log(`ℹ️ ${message}`, data);
                break;
        }
    };

    window.isValidEventId = function(eventId) {
        if (!eventId || typeof eventId !== 'string') return false;
        const trimmed = eventId.trim();
        return trimmed.length > 0 && !/[<>"]/.test(trimmed);
    };

    window.rateLimitDelay = function(delayMs = 1000) {
        return new Promise(resolve => setTimeout(resolve, delayMs));
    };
}

/**
 * Update progress bar
 */
function updateProgress(percentage, text) {
    progressFill.style.width = percentage + '%';
    progressText.textContent = text;
}

/**
 * Handle clear button
 */
function handleClear() {
    console.log('Clearing form and results');
    csvInput.value = '';
    hideAllSections();
    currentData = [];
    demoData.style.display = 'none';
}

/**
 * Handle search input
 */
function handleSearch() {
    const searchTerm = searchInput.value.toLowerCase();
    console.log('Searching for:', searchTerm);

    const rows = resultsTableBody.querySelectorAll('tr');
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
}

/**
 * Handle table sorting
 */
function handleSort(column) {
    console.log('Sorting by column:', column);

    if (sortColumn === column) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        sortColumn = column;
        sortDirection = 'asc';
    }

    const headers = document.querySelectorAll('.sortable');
    headers.forEach(header => {
        header.classList.remove('sort-asc', 'sort-desc');
        if (header.dataset.sort === column) {
            header.classList.add(sortDirection === 'asc' ? 'sort-asc' : 'sort-desc');
        }
    });

    const sortedData = [...currentData].sort((a, b) => {
        let aVal, bVal;

        switch (column) {
            case 'eventTitle':
                aVal = a.eventTitle;
                bVal = b.eventTitle;
                break;
            case 'eventStart':
                aVal = new Date(a.eventStart);
                bVal = new Date(b.eventStart);
                break;
            case 'participantName':
                aVal = a.participantName;
                bVal = b.participantName;
                break;
            case 'participantEmail':
                aVal = a.participantEmail || '';
                bVal = b.participantEmail || '';
                break;
            case 'totalDuration':
                aVal = a.totalDuration;
                bVal = b.totalDuration;
                break;
            case 'sessionCount':
                aVal = a.sessionCount;
                bVal = b.sessionCount;
                break;
            default:
                return 0;
        }

        if (aVal < bVal) return sortDirection === 'asc' ? -1 : 1;
        if (aVal > bVal) return sortDirection === 'asc' ? 1 : -1;
        return 0;
    });

    renderTable(sortedData);
}

/**
 * Handle CSV export
 */
function handleExport() {
    console.log('Exporting data to CSV');

    if (currentData.length === 0) {
        alert('No data to export');
        return;
    }

    const headers = ['Event Title', 'Event Date', 'Participant Name', 'Email', 'Duration (min)', 'Sessions', 'Join/Leave Times'];
    const csvContent = [
        headers.join(','),
        ...currentData.map(row => [
            `"${row.eventTitle}"`,
            `"${formatDateTime(row.eventStart)}"`,
            `"${row.participantName}"`,
            `"${row.participantEmail || ''}"`,
            row.totalDuration,
            row.sessionCount,
            `"${row.sessionTimes}"`
        ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `meet_attendance_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    console.log('CSV export completed');
}

/**
 * Display results
 */
function displayResults(result) {
    console.log('Displaying results:', result);

    if (result.errors && result.errors.length > 0) {
        displayErrors(result.errors);
    }

    if (result.summary) {
        displaySummary(result.summary);
    }

    if (result.data && result.data.length > 0) {
        currentData = processDataForTable(result.data);
        renderTable(currentData);
        showResultsSection();
    } else {
        displayError('No attendance data found for the provided event IDs');
    }
}

/**
 * Process data for table display
 */
function processDataForTable(data) {
    console.log('Processing data for table display');

    const tableData = [];

    data.forEach(event => {
        event.participants.forEach(participant => {
            tableData.push({
                eventTitle: event.eventTitle,
                eventStart: event.eventStart,
                participantName: participant.name,
                participantEmail: participant.email,
                totalDuration: participant.totalDuration,
                sessionCount: participant.sessions.length,
                sessionTimes: participant.sessions.map(session =>
                    `${formatTime(session.startTime)} - ${formatTime(session.endTime)}`
                ).join('; ')
            });
        });
    });

    console.log(`Processed ${tableData.length} participant records`);
    return tableData;
}

/**
 * Render table with data
 */
function renderTable(data) {
    console.log('Rendering table with', data.length, 'rows');

    resultsTableBody.innerHTML = '';

    data.forEach(row => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${escapeHtml(row.eventTitle)}</td>
            <td>${formatDateTime(row.eventStart)}</td>
            <td>${escapeHtml(row.participantName)}</td>
            <td>${escapeHtml(row.participantEmail || 'N/A')}</td>
            <td>${row.totalDuration}</td>
            <td>${row.sessionCount}</td>
            <td class="session-details">${escapeHtml(row.sessionTimes)}</td>
        `;
        resultsTableBody.appendChild(tr);
    });
}

/**
 * Display summary statistics
 */
function displaySummary(summary) {
    console.log('Displaying summary:', summary);

    summaryGrid.innerHTML = `
        <div class="summary-item">
            <span class="summary-value">${summary.totalEvents}</span>
            <span class="summary-label">Total Events</span>
        </div>
        <div class="summary-item">
            <span class="summary-value">${summary.totalParticipants}</span>
            <span class="summary-label">Total Participants</span>
        </div>
        <div class="summary-item">
            <span class="summary-value">${summary.uniqueParticipantCount}</span>
            <span class="summary-label">Unique Participants</span>
        </div>
        <div class="summary-item">
            <span class="summary-value">${summary.averageParticipantsPerEvent}</span>
            <span class="summary-label">Avg Participants/Event</span>
        </div>
        <div class="summary-item">
            <span class="summary-value">${summary.totalAttendanceMinutes}</span>
            <span class="summary-label">Total Minutes</span>
        </div>
        <div class="summary-item">
            <span class="summary-value">${summary.averageAttendancePerParticipant}</span>
            <span class="summary-label">Avg Minutes/Participant</span>
        </div>
    `;

    showSummarySection();
}

/**
 * Display errors
 */
function displayErrors(errors) {
    console.log('Displaying errors:', errors);

    errorList.innerHTML = errors.map(error =>
        `<div class="error-item">
            <strong>Event ID:</strong> ${escapeHtml(error.eventId)}<br>
            <strong>Error:</strong> ${escapeHtml(error.error)}
        </div>`
    ).join('');

    showErrorSection();
}

/**
 * Display single error
 */
function displayError(message) {
    console.error('Displaying error:', message);

    errorList.innerHTML = `<div class="error-item">${escapeHtml(message)}</div>`;
    showErrorSection();
}

// Utility functions
function setLoadingState(loading) {
    submitBtn.disabled = loading;
    const btnText = submitBtn.querySelector('.btn-text');
    const spinner = submitBtn.querySelector('.loading-spinner');

    if (loading) {
        btnText.textContent = 'Processing...';
        spinner.style.display = 'inline-block';
    } else {
        btnText.textContent = 'Process Events';
        spinner.style.display = 'none';
    }
}

function hideAllSections() {
    progressSection.style.display = 'none';
    errorSection.style.display = 'none';
    summarySection.style.display = 'none';
    resultsSection.style.display = 'none';
}

function showProgressSection() {
    progressSection.style.display = 'block';
}

function hideProgressSection() {
    progressSection.style.display = 'none';
}

function showErrorSection() {
    errorSection.style.display = 'block';
}

function showSummarySection() {
    summarySection.style.display = 'block';
}

function showResultsSection() {
    resultsSection.style.display = 'block';
}

function formatDateTime(dateString) {
    return new Date(dateString).toLocaleString();
}

function formatTime(dateString) {
    return new Date(dateString).toLocaleTimeString();
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// ============================================================================
// LOCAL VERSION SPECIFIC FUNCTIONS
// ============================================================================

/**
 * Get application version and environment info for local version
 * @return {Object} - Version and environment information
 */
function getAppInfo() {
    return {
        version: '2.0.0',
        environment: getEnvironment(),
        timestamp: new Date().toISOString(),
        features: {
            meetApiIntegration: true,
            calendarApiIntegration: true,
            browserAuth: true,
            enhancedLogging: true,
            dualVersionSupport: true,
            demoMode: true
        },
        apiStatus: {
            gapiLoaded: typeof gapi !== 'undefined',
            clientReady: apiClientReady,
            authenticated: isAuthenticated,
            hasToken: !!accessToken
        }
    };
}

/**
 * Debug function to check API status (development only)
 */
function debugApiStatus() {
    console.log('=== Local Version API Status ===');
    const info = getAppInfo();
    console.log('Environment:', info.environment);
    console.log('Version:', info.version);
    console.log('API Status:', info.apiStatus);
    console.log('Features:', info.features);
    console.log('================================');
    return info;
}

// Make debug functions available globally for development
if (typeof window !== 'undefined') {
    window.debugApiStatus = debugApiStatus;
    window.getAppInfo = getAppInfo;
}

enhancedLog('info', '🚀 Local version loaded successfully', {
    environment: getEnvironment(),
    version: '2.0.0'
});

console.log('💡 Development helpers available:');
console.log('  - debugApiStatus() - Check API status');
console.log('  - getAppInfo() - Get application information');
