<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Meet Attendance Export - Local Version</title>
    <link rel="stylesheet" href="../style.css">
    <style>
        /* Additional styles for local version */
        .auth-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .auth-section h3 {
            color: #856404;
            margin-bottom: 10px;
        }

        .auth-button {
            background-color: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }

        .auth-button:hover {
            background-color: #3367d6;
        }

        .auth-status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }

        .auth-status.authenticated {
            background-color: #d4edda;
            color: #155724;
        }

        .auth-status.not-authenticated {
            background-color: #f8d7da;
            color: #721c24;
        }

        .demo-section {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .demo-section h3 {
            color: #0056b3;
            margin-bottom: 10px;
        }

        .demo-data {
            font-family: 'Courier New', monospace;
            background: white;
            padding: 10px;
            border-radius: 3px;
            border: 1px solid #ddd;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Google Meet Attendance Export</h1>
            <p>Export attendance data for multiple Google Meet sessions using Calendar event IDs</p>
            <p><strong>Local Version</strong> - Uses Google APIs directly from the browser</p>
        </header>

        <main>
            <!-- Authentication Section -->
            <section class="auth-section">
                <h3>Google API Authentication</h3>
                <p>This application requires access to your Google Calendar and Meet data.</p>
                <button id="authButton" class="auth-button">Sign In with Google</button>
                <button id="signoutButton" class="auth-button" style="display: none;">Sign Out</button>
                <span id="authStatus" class="auth-status not-authenticated">Not Authenticated</span>
            </section>

            <!-- Demo Section -->
            <section class="demo-section">
                <h3>Demo Mode</h3>
                <p>Try the application with sample data (no authentication required):</p>
                <button id="loadDemoBtn" class="btn btn-secondary">Load Demo Data</button>
                <div class="demo-data" style="display: none;" id="demoData">
                    demo_event_1,demo_event_2,demo_event_3
                </div>
            </section>

            <section class="input-section">
                <h2>Input Event IDs</h2>
                <form id="eventForm">
                    <div class="form-group">
                        <label for="csvInput">
                            Enter Google Calendar Event IDs (CSV format):
                            <span class="help-text">One event ID per line, or comma-separated</span>
                        </label>
                        <textarea
                            id="csvInput"
                            name="csvInput"
                            rows="10"
                            placeholder="event_id_1&#10;event_id_2&#10;event_id_3&#10;&#10;Or: event_id_1,event_id_2,event_id_3"
                            required
                        ></textarea>
                    </div>

                    <div class="form-actions">
                        <button type="submit" id="submitBtn" class="btn btn-primary">
                            <span class="btn-text">Process Events</span>
                            <span class="loading-spinner" style="display: none;"></span>
                        </button>
                        <button type="button" id="clearBtn" class="btn btn-secondary">Clear</button>
                    </div>
                </form>
            </section>

            <section class="progress-section" id="progressSection" style="display: none;">
                <h3>Processing Progress</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <p id="progressText">Initializing...</p>
            </section>

            <section class="error-section" id="errorSection" style="display: none;">
                <h3>Errors</h3>
                <div id="errorList" class="error-list"></div>
            </section>

            <section class="summary-section" id="summarySection" style="display: none;">
                <h3>Summary Statistics</h3>
                <div class="summary-grid" id="summaryGrid"></div>
            </section>

            <section class="results-section" id="resultsSection" style="display: none;">
                <div class="results-header">
                    <h3>Attendance Results</h3>
                    <div class="results-controls">
                        <input type="text" id="searchInput" placeholder="Search participants..." class="search-input">
                        <button id="exportBtn" class="btn btn-secondary">Export CSV</button>
                    </div>
                </div>

                <div class="table-container">
                    <table id="resultsTable" class="results-table">
                        <thead>
                            <tr>
                                <th data-sort="eventTitle" class="sortable">Event Title</th>
                                <th data-sort="eventStart" class="sortable">Date/Time</th>
                                <th data-sort="participantName" class="sortable">Participant Name</th>
                                <th data-sort="participantEmail" class="sortable">Email</th>
                                <th data-sort="totalDuration" class="sortable">Duration (min)</th>
                                <th data-sort="sessionCount" class="sortable">Sessions</th>
                                <th>Join/Leave Times</th>
                            </tr>
                        </thead>
                        <tbody id="resultsTableBody">
                        </tbody>
                    </table>
                </div>
            </section>
        </main>
    </div>

    <!-- Google Identity Services (new auth library) -->
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <!-- Google API Client Library -->
    <script src="https://apis.google.com/js/api.js"></script>
    <!-- Shared utilities -->
    <script src="../shared-utils.js"></script>
    <!-- Configuration (optional) -->
    <script src="config.js"></script>
    <!-- Main application script -->
    <script src="local-script.js"></script>
</body>
</html>
