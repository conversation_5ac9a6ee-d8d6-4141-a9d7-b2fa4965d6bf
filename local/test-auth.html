<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Google Identity Services</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #3367d6; }
        button:disabled { opacity: 0.6; cursor: not-allowed; }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Google Identity Services Test</h1>
    
    <div class="info">
        <strong>Instructions:</strong>
        <ol>
            <li>Update CLIENT_ID below with your actual Google OAuth client ID</li>
            <li>Make sure you're running this from a web server (not file://)</li>
            <li>Ensure your OAuth client has http://localhost:8000 in authorized origins</li>
        </ol>
    </div>

    <div id="status" class="status info">
        Status: Initializing...
    </div>

    <div>
        <button id="authBtn" onclick="authenticate()" disabled>Sign In with Google</button>
        <button id="signoutBtn" onclick="signOut()" disabled>Sign Out</button>
        <button onclick="testCalendarAPI()" id="testBtn" disabled>Test Calendar API</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <div id="log"></div>

    <!-- Google Identity Services -->
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <!-- Google API Client -->
    <script src="https://apis.google.com/js/api.js"></script>

    <script>
        // Configuration - UPDATE THESE VALUES
        const CLIENT_ID = 'your-client-id.apps.googleusercontent.com';
        const API_KEY = 'your-api-key';
        const SCOPES = 'https://www.googleapis.com/auth/calendar.readonly';

        // Global variables
        let gisClient = null;
        let accessToken = null;

        // Initialize when page loads
        window.addEventListener('load', function() {
            log('Page loaded, initializing...');
            
            if (CLIENT_ID === 'your-client-id.apps.googleusercontent.com') {
                updateStatus('error', 'Please update CLIENT_ID and API_KEY in the script');
                return;
            }

            // Initialize Google API client
            gapi.load('client', initGapiClient);
            
            // Initialize Google Identity Services
            initGoogleIdentity();
        });

        function initGapiClient() {
            log('Initializing Google API client...');
            
            gapi.client.init({
                apiKey: API_KEY,
                discoveryDocs: ['https://www.googleapis.com/discovery/v1/apis/calendar/v3/rest']
            }).then(function() {
                log('✅ Google API client initialized');
                updateStatus('success', 'Google API client ready');
            }).catch(function(error) {
                log('❌ Error initializing Google API client: ' + error.message);
                updateStatus('error', 'Failed to initialize Google API client');
            });
        }

        function initGoogleIdentity() {
            log('Initializing Google Identity Services...');
            
            try {
                // Initialize OAuth for API access
                gisClient = google.accounts.oauth2.initTokenClient({
                    client_id: CLIENT_ID,
                    scope: SCOPES,
                    callback: handleTokenResponse
                });
                
                log('✅ Google Identity Services initialized');
                updateStatus('success', 'Ready for authentication');
                document.getElementById('authBtn').disabled = false;
                
            } catch (error) {
                log('❌ Error initializing Google Identity Services: ' + error.message);
                updateStatus('error', 'Failed to initialize Google Identity Services');
            }
        }

        function handleTokenResponse(response) {
            log('Token response received: ' + JSON.stringify(response, null, 2));
            
            if (response.access_token) {
                accessToken = response.access_token;
                
                // Set token for gapi client
                gapi.client.setToken({
                    access_token: accessToken
                });
                
                log('✅ Authentication successful');
                updateStatus('success', 'Authenticated successfully');
                
                document.getElementById('authBtn').disabled = true;
                document.getElementById('signoutBtn').disabled = false;
                document.getElementById('testBtn').disabled = false;
                
            } else {
                log('❌ No access token received');
                updateStatus('error', 'Authentication failed');
            }
        }

        function authenticate() {
            log('Starting authentication...');
            updateStatus('info', 'Authenticating...');
            
            if (gisClient) {
                gisClient.requestAccessToken();
            } else {
                log('❌ Google Identity Services not initialized');
                updateStatus('error', 'Authentication system not ready');
            }
        }

        function signOut() {
            log('Signing out...');
            
            if (accessToken) {
                google.accounts.oauth2.revoke(accessToken, () => {
                    log('✅ Token revoked');
                });
                
                gapi.client.setToken(null);
                accessToken = null;
                
                updateStatus('info', 'Signed out');
                
                document.getElementById('authBtn').disabled = false;
                document.getElementById('signoutBtn').disabled = true;
                document.getElementById('testBtn').disabled = true;
            }
        }

        async function testCalendarAPI() {
            log('Testing Calendar API...');
            updateStatus('info', 'Testing API access...');
            
            try {
                const response = await gapi.client.calendar.calendarList.list({
                    maxResults: 5
                });
                
                log('✅ Calendar API test successful');
                log('Found ' + response.result.items.length + ' calendars');
                
                response.result.items.forEach(calendar => {
                    log('  - ' + calendar.summary);
                });
                
                updateStatus('success', 'Calendar API working correctly');
                
            } catch (error) {
                log('❌ Calendar API test failed: ' + error.message);
                updateStatus('error', 'Calendar API test failed');
            }
        }

        function updateStatus(type, message) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = 'status ' + type;
            statusDiv.textContent = 'Status: ' + message;
        }

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
    </script>
</body>
</html>
