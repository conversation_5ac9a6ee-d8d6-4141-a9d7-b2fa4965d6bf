# Google Meet Attendance Export

A web application that exports attendance data for multiple Google Meet sessions using Google Calendar event IDs. Available in two versions:

- **Apps Script Version** - Runs on Google Apps Script (recommended for most users)
- **Local Version** - Runs locally in your browser with Google API integration

## Features

- **CSV Input**: Accept Google Calendar event IDs in CSV format
- **Batch Processing**: Process multiple meetings at once with rate limiting
- **Detailed Attendance Data**: Extract participant names, emails, join/leave times, and duration
- **Summary Statistics**: Display aggregated data across all meetings
- **Sortable Table**: Interactive results table with search and sort functionality
- **CSV Export**: Export results to CSV format
- **Error Handling**: Comprehensive error reporting for failed events
- **Verbose Logging**: Detailed console logging for debugging

## Version Comparison

| Feature | Apps Script Version | Local Version |
|---------|-------------------|---------------|
| **Setup Complexity** | Low (built-in auth) | High (OAuth setup) |
| **Meet API Access** | Full server-side access | Limited browser support |
| **Hosting** | Google-hosted | Self-hosted |
| **Authentication** | Automatic | OAuth 2.0 flow |
| **Customization** | Limited by Apps Script | Full control |
| **Rate Limits** | Apps Script quotas | Your project quotas |
| **Recommended For** | Most users | Developers, custom deployments |

## Quick Start

### Apps Script Version (Recommended)
1. Copy files to Google Apps Script project
2. Enable Calendar and Meet APIs
3. Deploy as web app
4. Start using immediately

### Local Version
1. Set up Google Cloud project and OAuth
2. Run local web server: `python local/server.py`
3. Configure API credentials
4. Open http://localhost:8000

## Apps Script Setup Instructions

### 1. Enable Required APIs

In your Google Apps Script project, you need to enable the following Advanced Google Services:

1. **Google Calendar API v3**
2. **Google Meet API v1**

To enable these:
1. Open your Apps Script project
2. Click on "Services" in the left sidebar (or "Libraries" in legacy editor)
3. Click "Add a service"
4. Find and add "Google Calendar API" and "Google Meet API"

### 2. Configure OAuth Scopes

The `appsscript.json` file includes the necessary OAuth scopes:
- `https://www.googleapis.com/auth/calendar.readonly`
- `https://www.googleapis.com/auth/meetings.space.readonly`
- `https://www.googleapis.com/auth/script.webapp.deploy`

### 3. Deploy as Web App

1. In Apps Script, click "Deploy" > "New deployment"
2. Choose "Web app" as the type
3. Set "Execute as" to "Me"
4. Set "Who has access" to "Anyone" (or as needed for your organization)
5. Click "Deploy"
6. Copy the web app URL

## Usage

### 1. Prepare Event IDs

Get Google Calendar event IDs for the meetings you want to analyze. Event IDs can be found:
- In the Google Calendar URL when viewing an event
- Using the Google Calendar API
- From calendar export data

### 2. Input Format

The application accepts event IDs in CSV format:

**Option 1: One per line**
```
event_id_1
event_id_2
event_id_3
```

**Option 2: Comma-separated**
```
event_id_1,event_id_2,event_id_3
```

### 3. Process Events

1. Open the web app URL
2. Paste your event IDs into the text area
3. Click "Process Events"
4. Wait for processing to complete (progress bar will show status)

### 4. View Results

The application will display:
- **Summary Statistics**: Total events, participants, attendance time, etc.
- **Detailed Table**: Individual participant records with join/leave times
- **Error Report**: Any events that couldn't be processed

### 5. Export Data

Click the "Export CSV" button to download the results as a CSV file.

## Data Structure

### Event Data
- Event ID
- Event Title
- Start/End Times
- Conference ID
- Meeting URI
- Participant Count

### Participant Data
- Name
- Email Address
- Total Duration (minutes)
- Number of Sessions
- Join/Leave Timestamps for each session

### Summary Statistics
- Total Events Processed
- Total Participants
- Unique Participants
- Average Participants per Event
- Total Attendance Minutes
- Average Attendance per Participant

## Troubleshooting

### Common Issues

1. **"Calendar API access failed"**
   - Ensure Google Calendar API is enabled
   - Check OAuth scopes in appsscript.json
   - Verify you have access to the calendars containing the events

2. **"Meet API access failed"**
   - Ensure Google Meet API is enabled
   - Note: Meet API may have limited availability
   - Check if your Google Workspace has Meet API access

3. **"Event not found"**
   - Verify the event ID is correct
   - Ensure you have access to the calendar containing the event
   - Check if the event has been deleted

4. **"No conference data found"**
   - The event must have Google Meet conferencing enabled
   - Only events with actual Meet sessions will have attendance data

### Testing

Use the built-in test functions:

```javascript
// Test API access
testSetup()

// Get a sample event for testing
getSampleEvent()
```

### Debugging

The application includes verbose console logging. Open browser developer tools to view detailed logs during processing.

## Limitations

1. **Meet API Availability**: The Google Meet API may not be available for all Google Workspace accounts
2. **Historical Data**: Attendance data is only available for meetings that have already occurred
3. **Rate Limits**: The application includes delays between API calls to respect rate limits
4. **Conference Records**: Only meetings with recorded attendance data will return results

## File Structure

### Apps Script Version
- `Code.js` - Backend logic and Google API integration
- `index.html` - Main web interface
- `style.css` - Styling and responsive design
- `script.js` - Frontend JavaScript for user interactions
- `appsscript.json` - Project configuration and OAuth scopes

### Local Version
- `local/index.html` - Local web interface
- `local/local-script.js` - JavaScript with Google API integration
- `local/config.js` - Configuration file for API credentials
- `local/server.py` - Python development server
- `local/package.json` - Node.js configuration
- `local/LOCAL_SETUP.md` - Detailed local setup guide

### Shared Files
- `style.css` - Shared styling (used by both versions)
- `README.md` - This documentation
- `DEPLOYMENT.md` - Apps Script deployment guide

## Security Notes

- The application requires read-only access to Calendar and Meet data
- Event IDs and attendance data are processed server-side
- No data is stored permanently; all processing is done in real-time
- Consider access permissions when deploying the web app

## Support

For issues or questions:
1. Check the browser console for detailed error messages
2. Use the test functions to verify API access
3. Ensure all required APIs are enabled and properly configured
4. Verify OAuth scopes match the requirements
