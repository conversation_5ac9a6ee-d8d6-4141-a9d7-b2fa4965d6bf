/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

header h1 {
    color: #1a73e8;
    margin-bottom: 10px;
    font-size: 2.5em;
}

header p {
    color: #666;
    font-size: 1.1em;
}

/* Sections */
section {
    background: white;
    margin-bottom: 20px;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

section h2, section h3 {
    color: #1a73e8;
    margin-bottom: 15px;
    border-bottom: 2px solid #e8f0fe;
    padding-bottom: 10px;
}

/* Form styles */
.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.help-text {
    font-size: 0.9em;
    color: #666;
    font-weight: normal;
    display: block;
    margin-top: 4px;
}

textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    resize: vertical;
    transition: border-color 0.3s;
}

textarea:focus {
    outline: none;
    border-color: #1a73e8;
}

/* Button styles */
.form-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background-color: #1a73e8;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: #1557b0;
}

.btn-secondary {
    background-color: #f8f9fa;
    color: #333;
    border: 1px solid #ddd;
}

.btn-secondary:hover:not(:disabled) {
    background-color: #e9ecef;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Loading spinner */
.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Progress bar */
.progress-bar {
    width: 100%;
    height: 20px;
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background-color: #1a73e8;
    transition: width 0.3s ease;
    width: 0%;
}

#progressText {
    color: #666;
    font-size: 14px;
}

/* Error styles */
.error-list {
    background-color: #fef7f7;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    padding: 15px;
}

.error-item {
    margin-bottom: 10px;
    padding: 10px;
    background-color: #f8d7da;
    border-radius: 4px;
    color: #721c24;
}

.error-item:last-child {
    margin-bottom: 0;
}

/* Summary grid */
.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.summary-item {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    text-align: center;
    border-left: 4px solid #1a73e8;
}

.summary-value {
    font-size: 2em;
    font-weight: bold;
    color: #1a73e8;
    display: block;
}

.summary-label {
    color: #666;
    font-size: 0.9em;
    margin-top: 5px;
}

/* Results section */
.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.results-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    width: 200px;
}

/* Table styles */
.table-container {
    overflow-x: auto;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.results-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.results-table th,
.results-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.results-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
    position: sticky;
    top: 0;
    z-index: 10;
}

.results-table th.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
}

.results-table th.sortable:hover {
    background-color: #e9ecef;
}

.results-table th.sortable::after {
    content: '↕';
    position: absolute;
    right: 8px;
    opacity: 0.5;
}

.results-table th.sort-asc::after {
    content: '↑';
    opacity: 1;
}

.results-table th.sort-desc::after {
    content: '↓';
    opacity: 1;
}

.results-table tbody tr:hover {
    background-color: #f8f9fa;
}

.session-details {
    font-size: 0.85em;
    color: #666;
}

.session-time {
    display: block;
    margin-bottom: 2px;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    .results-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .results-controls {
        justify-content: space-between;
    }
    
    .search-input {
        width: 100%;
        max-width: 300px;
    }
    
    .summary-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .btn {
        justify-content: center;
    }
}
