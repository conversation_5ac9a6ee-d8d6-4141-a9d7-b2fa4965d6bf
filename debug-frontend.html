/**
 * Debug functions for troubleshooting the blank page issue
 * These functions can be called from the browser console
 */

// Test basic functionality
function testBasicFunctionality() {
    console.log('🧪 Testing basic functionality...');
    
    try {
        // Test DOM access
        const form = document.getElementById('eventForm');
        const input = document.getElementById('csvInput');
        const button = document.getElementById('submitBtn');
        
        console.log('✅ DOM elements:', {
            form: !!form,
            input: !!input,
            button: !!button
        });
        
        // Test form submission without backend call
        if (input) {
            input.value = 'test-event-id';
            console.log('✅ Set test value in input');
        }
        
        // Test UI functions
        if (typeof hideAllSections === 'function') {
            hideAllSections();
            console.log('✅ hideAllSections works');
        }
        
        if (typeof setLoadingState === 'function') {
            setLoadingState(true);
            setTimeout(() => setLoadingState(false), 1000);
            console.log('✅ setLoadingState works');
        }
        
        console.log('🎉 Basic functionality test completed');
        
    } catch (error) {
        console.error('❌ Basic functionality test failed:', error);
    }
}

// Test backend communication
function testBackendCommunication() {
    console.log('🧪 Testing backend communication...');
    
    try {
        // Check if Google Apps Script runtime is available
        if (typeof google === 'undefined') {
            console.error('❌ Google Apps Script runtime not available');
            return;
        }
        
        if (!google.script || !google.script.run) {
            console.error('❌ google.script.run not available');
            return;
        }
        
        console.log('✅ Google Apps Script runtime available');
        
        // Test simple backend call
        google.script.run
            .withSuccessHandler((result) => {
                console.log('✅ Backend test call successful:', result);
            })
            .withFailureHandler((error) => {
                console.error('❌ Backend test call failed:', error);
            })
            .testSetup();
            
        console.log('🔄 Backend test call initiated...');
        
    } catch (error) {
        console.error('❌ Backend communication test failed:', error);
    }
}

// Test error handling
function testErrorHandling() {
    console.log('🧪 Testing error handling...');
    
    try {
        // Trigger a controlled error
        throw new Error('Test error for debugging');
    } catch (error) {
        console.log('✅ Error caught successfully:', error.message);
        
        // Test error display function
        if (typeof displayError === 'function') {
            displayError('Test error message');
            console.log('✅ displayError function works');
        } else {
            console.error('❌ displayError function not available');
        }
    }
}

// Simulate form submission without backend
function simulateFormSubmission() {
    console.log('🧪 Simulating form submission...');
    
    try {
        const csvInput = document.getElementById('csvInput');
        if (!csvInput) {
            throw new Error('CSV input not found');
        }
        
        // Set test data
        csvInput.value = 'test-event-1\ntest-event-2';
        
        // Simulate the UI changes that happen during submission
        console.log('🔄 Simulating UI changes...');
        
        if (typeof hideAllSections === 'function') {
            hideAllSections();
        }
        
        if (typeof setLoadingState === 'function') {
            setLoadingState(true);
        }
        
        if (typeof showProgressSection === 'function') {
            showProgressSection();
        }
        
        // Simulate completion after 2 seconds
        setTimeout(() => {
            console.log('🔄 Simulating completion...');
            
            if (typeof setLoadingState === 'function') {
                setLoadingState(false);
            }
            
            if (typeof hideProgressSection === 'function') {
                hideProgressSection();
            }
            
            // Show a test error
            if (typeof displayError === 'function') {
                displayError('Simulated test completed - this is not a real error');
            }
            
            console.log('✅ Form submission simulation completed');
        }, 2000);
        
    } catch (error) {
        console.error('❌ Form submission simulation failed:', error);
    }
}

// Check for common issues
function checkCommonIssues() {
    console.log('🔍 Checking for common issues...');
    
    const issues = [];
    
    // Check if all required DOM elements exist
    const requiredElements = [
        'eventForm', 'csvInput', 'submitBtn', 'clearBtn',
        'progressSection', 'errorSection', 'summarySection', 'resultsSection'
    ];
    
    requiredElements.forEach(id => {
        if (!document.getElementById(id)) {
            issues.push(`Missing DOM element: ${id}`);
        }
    });
    
    // Check if functions are defined
    const requiredFunctions = [
        'handleFormSubmit', 'displayError', 'hideAllSections', 
        'setLoadingState', 'showProgressSection'
    ];
    
    requiredFunctions.forEach(funcName => {
        if (typeof window[funcName] !== 'function') {
            issues.push(`Missing function: ${funcName}`);
        }
    });
    
    // Check Google Apps Script availability
    if (typeof google === 'undefined' || !google.script) {
        issues.push('Google Apps Script runtime not available');
    }
    
    if (issues.length === 0) {
        console.log('✅ No common issues found');
    } else {
        console.warn('⚠️ Issues found:', issues);
    }
    
    return issues;
}

// Run all tests
function runAllDebugTests() {
    console.log('🚀 Running all debug tests...');
    console.log('=====================================');
    
    checkCommonIssues();
    console.log('');
    
    testBasicFunctionality();
    console.log('');
    
    testErrorHandling();
    console.log('');
    
    testBackendCommunication();
    console.log('');
    
    console.log('=====================================');
    console.log('🎉 All debug tests completed!');
    console.log('💡 You can also run:');
    console.log('  - simulateFormSubmission() - Test form without backend');
    console.log('  - debugEnvironment() - Check environment status');
}

// Make functions globally available
if (typeof window !== 'undefined') {
    window.testBasicFunctionality = testBasicFunctionality;
    window.testBackendCommunication = testBackendCommunication;
    window.testErrorHandling = testErrorHandling;
    window.simulateFormSubmission = simulateFormSubmission;
    window.checkCommonIssues = checkCommonIssues;
    window.runAllDebugTests = runAllDebugTests;
}

console.log('🛠️ Debug functions loaded. Run runAllDebugTests() to start debugging.');
