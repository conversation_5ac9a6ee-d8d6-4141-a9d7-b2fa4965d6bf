/**
 * Google Meet Attendance Export - Apps Script Frontend
 * Browser-side JavaScript for the Apps Script web app
 */

// Check if we're in a browser environment
if (typeof document !== 'undefined') {
    // Global variables
    let currentData = [];
    let sortColumn = '';
    let sortDirection = 'asc';

    // DOM elements - only access in browser environment
    let eventForm, csvInput, submitBtn, clearBtn, progressSection, progressFill, progressText;
    let errorSection, errorList, summarySection, summaryGrid, resultsSection, resultsTable;
    let resultsTableBody, searchInput, exportBtn;

    // Initialize DOM elements when document is ready
    function initializeDOMElements() {
        eventForm = document.getElementById('eventForm');
        csvInput = document.getElementById('csvInput');
        submitBtn = document.getElementById('submitBtn');
        clearBtn = document.getElementById('clearBtn');
        progressSection = document.getElementById('progressSection');
        progressFill = document.getElementById('progressFill');
        progressText = document.getElementById('progressText');
        errorSection = document.getElementById('errorSection');
        errorList = document.getElementById('errorList');
        summarySection = document.getElementById('summarySection');
        summaryGrid = document.getElementById('summaryGrid');
        resultsSection = document.getElementById('resultsSection');
        resultsTable = document.getElementById('resultsTable');
        resultsTableBody = document.getElementById('resultsTableBody');
        searchInput = document.getElementById('searchInput');
        exportBtn = document.getElementById('exportBtn');
    }

    // Event listeners
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 Apps Script frontend initializing...');

        // Initialize DOM elements
        initializeDOMElements();

        // Add event listeners
        if (eventForm) eventForm.addEventListener('submit', handleFormSubmit);
        if (clearBtn) clearBtn.addEventListener('click', handleClear);
        if (searchInput) searchInput.addEventListener('input', handleSearch);
        if (exportBtn) exportBtn.addEventListener('click', handleExport);

        // Add sort listeners to table headers
        const sortableHeaders = document.querySelectorAll('.sortable');
        sortableHeaders.forEach(header => {
            header.addEventListener('click', () => handleSort(header.dataset.sort));
        });

        console.log('✅ Apps Script frontend initialized successfully');
    });

    /**
     * Handle form submission
     */
    async function handleFormSubmit(event) {
        event.preventDefault();
        console.log('📝 Form submitted');

        const csvData = csvInput.value.trim();
        if (!csvData) {
            alert('Please enter some event IDs');
            return;
        }

        console.log('📊 CSV data to process:', csvData);

        // Reset UI
        hideAllSections();
        setLoadingState(true);
        showProgressSection();

        try {
            // Call the backend function
            console.log('🔄 Calling processEventIds...');
            const result = await new Promise((resolve, reject) => {
                google.script.run
                    .withSuccessHandler(resolve)
                    .withFailureHandler(reject)
                    .processEventIds(csvData);
            });

            console.log('✅ Backend response received:', result);

            if (result.success) {
                displayResults(result);
            } else {
                displayError('Processing failed: ' + result.error);
            }

        } catch (error) {
            console.error('❌ Error calling backend:', error);
            displayError('Failed to process events: ' + error.message);
        } finally {
            setLoadingState(false);
            hideProgressSection();
        }
    }

    /**
     * Handle clear button
     */
    function handleClear() {
        console.log('🧹 Clearing form and results');
        csvInput.value = '';
        hideAllSections();
        currentData = [];
    }

    /**
     * Handle search input
     */
    function handleSearch() {
        const searchTerm = searchInput.value.toLowerCase();
        console.log('🔍 Searching for:', searchTerm);

        const rows = resultsTableBody.querySelectorAll('tr');
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    }

    /**
     * Handle table sorting
     */
    function handleSort(column) {
        console.log('📊 Sorting by column:', column);

        if (sortColumn === column) {
            sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            sortColumn = column;
            sortDirection = 'asc';
        }

        // Update header classes
        const headers = document.querySelectorAll('.sortable');
        headers.forEach(header => {
            header.classList.remove('sort-asc', 'sort-desc');
            if (header.dataset.sort === column) {
                header.classList.add(sortDirection === 'asc' ? 'sort-asc' : 'sort-desc');
            }
        });

        // Sort the data
        const sortedData = [...currentData].sort((a, b) => {
            let aVal, bVal;

            switch (column) {
                case 'eventTitle':
                    aVal = a.eventTitle;
                    bVal = b.eventTitle;
                    break;
                case 'eventStart':
                    aVal = new Date(a.eventStart);
                    bVal = new Date(b.eventStart);
                    break;
                case 'participantName':
                    aVal = a.participantName;
                    bVal = b.participantName;
                    break;
                case 'participantEmail':
                    aVal = a.participantEmail || '';
                    bVal = b.participantEmail || '';
                    break;
                case 'totalDuration':
                    aVal = a.totalDuration;
                    bVal = b.totalDuration;
                    break;
                case 'sessionCount':
                    aVal = a.sessionCount;
                    bVal = b.sessionCount;
                    break;
                default:
                    return 0;
            }

            if (aVal < bVal) return sortDirection === 'asc' ? -1 : 1;
            if (aVal > bVal) return sortDirection === 'asc' ? 1 : -1;
            return 0;
        });

        renderTable(sortedData);
    }

    /**
     * Handle CSV export
     */
    function handleExport() {
        console.log('📤 Exporting data to CSV');

        if (currentData.length === 0) {
            alert('No data to export');
            return;
        }

        const headers = ['Event Title', 'Event Date', 'Participant Name', 'Email', 'Duration (min)', 'Sessions', 'Join/Leave Times'];
        const csvContent = [
            headers.join(','),
            ...currentData.map(row => [
                `"${row.eventTitle}"`,
                `"${formatDateTime(row.eventStart)}"`,
                `"${row.participantName}"`,
                `"${row.participantEmail || ''}"`,
                row.totalDuration,
                row.sessionCount,
                `"${row.sessionTimes}"`
            ].join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `meet_attendance_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        console.log('✅ CSV export completed');
    }

    /**
     * Display results
     */
    function displayResults(result) {
        console.log('📊 Displaying results:', result);

        // Show errors if any
        if (result.errors && result.errors.length > 0) {
            displayErrors(result.errors);
        }

        // Show summary
        if (result.summary) {
            displaySummary(result.summary);
        }

        // Process and display attendance data
        if (result.data && result.data.length > 0) {
            currentData = processDataForTable(result.data);
            renderTable(currentData);
            showResultsSection();
        } else {
            displayError('No attendance data found for the provided event IDs');
        }
    }

    /**
     * Process data for table display
     */
    function processDataForTable(data) {
        console.log('🔄 Processing data for table display');

        const tableData = [];

        data.forEach(event => {
            event.participants.forEach(participant => {
                tableData.push({
                    eventTitle: event.eventTitle,
                    eventStart: event.eventStart,
                    participantName: participant.name,
                    participantEmail: participant.email,
                    totalDuration: participant.totalDuration,
                    sessionCount: participant.sessions.length,
                    sessionTimes: participant.sessions.map(session =>
                        `${formatTime(session.startTime)} - ${formatTime(session.endTime)}`
                    ).join('; ')
                });
            });
        });

        console.log(`✅ Processed ${tableData.length} participant records`);
        return tableData;
    }

    /**
     * Render table with data
     */
    function renderTable(data) {
        console.log('📋 Rendering table with', data.length, 'rows');

        resultsTableBody.innerHTML = '';

        data.forEach(row => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>${escapeHtml(row.eventTitle)}</td>
                <td>${formatDateTime(row.eventStart)}</td>
                <td>${escapeHtml(row.participantName)}</td>
                <td>${escapeHtml(row.participantEmail || 'N/A')}</td>
                <td>${row.totalDuration}</td>
                <td>${row.sessionCount}</td>
                <td class="session-details">${escapeHtml(row.sessionTimes)}</td>
            `;
            resultsTableBody.appendChild(tr);
        });
    }

    /**
     * Display summary statistics
     */
    function displaySummary(summary) {
        console.log('📈 Displaying summary:', summary);

        summaryGrid.innerHTML = `
            <div class="summary-item">
                <span class="summary-value">${summary.totalEvents}</span>
                <span class="summary-label">Total Events</span>
            </div>
            <div class="summary-item">
                <span class="summary-value">${summary.totalParticipants}</span>
                <span class="summary-label">Total Participants</span>
            </div>
            <div class="summary-item">
                <span class="summary-value">${summary.uniqueParticipantCount}</span>
                <span class="summary-label">Unique Participants</span>
            </div>
            <div class="summary-item">
                <span class="summary-value">${summary.averageParticipantsPerEvent}</span>
                <span class="summary-label">Avg Participants/Event</span>
            </div>
            <div class="summary-item">
                <span class="summary-value">${summary.totalAttendanceMinutes}</span>
                <span class="summary-label">Total Minutes</span>
            </div>
            <div class="summary-item">
                <span class="summary-value">${summary.averageAttendancePerParticipant}</span>
                <span class="summary-label">Avg Minutes/Participant</span>
            </div>
        `;

        showSummarySection();
    }

    /**
     * Display errors
     */
    function displayErrors(errors) {
        console.log('⚠️ Displaying errors:', errors);

        errorList.innerHTML = errors.map(error =>
            `<div class="error-item">
                <strong>Event ID:</strong> ${escapeHtml(error.eventId)}<br>
                <strong>Error:</strong> ${escapeHtml(error.error)}
            </div>`
        ).join('');

        showErrorSection();
    }

    /**
     * Display single error
     */
    function displayError(message) {
        console.error('❌ Displaying error:', message);

        errorList.innerHTML = `<div class="error-item">${escapeHtml(message)}</div>`;
        showErrorSection();
    }

    // Utility functions
    function setLoadingState(loading) {
        submitBtn.disabled = loading;
        const btnText = submitBtn.querySelector('.btn-text');
        const spinner = submitBtn.querySelector('.loading-spinner');

        if (loading) {
            btnText.textContent = 'Processing...';
            spinner.style.display = 'inline-block';
        } else {
            btnText.textContent = 'Process Events';
            spinner.style.display = 'none';
        }
    }

    function hideAllSections() {
        progressSection.style.display = 'none';
        errorSection.style.display = 'none';
        summarySection.style.display = 'none';
        resultsSection.style.display = 'none';
    }

    function showProgressSection() {
        progressSection.style.display = 'block';
    }

    function hideProgressSection() {
        progressSection.style.display = 'none';
    }

    function showErrorSection() {
        errorSection.style.display = 'block';
    }

    function showSummarySection() {
        summarySection.style.display = 'block';
    }

    function showResultsSection() {
        resultsSection.style.display = 'block';
    }

    function formatDateTime(dateString) {
        return new Date(dateString).toLocaleString();
    }

    function formatTime(dateString) {
        return new Date(dateString).toLocaleTimeString();
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    console.log('✅ Apps Script frontend script loaded successfully');

} else {
    // Server-side environment (Apps Script backend)
    console.log('ℹ️ Script running in server-side environment - DOM functions not available');
}
